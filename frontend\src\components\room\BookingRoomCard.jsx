import React, { useState } from 'react';
import { Card, Typography, Space, Button, Image, Rate, Tag, Divider } from 'antd';
import {
  HeartOutlined,
  HeartFilled,
  StarFilled,
  EnvironmentOutlined,
  UserOutlined,
  WifiOutlined,
  CarOutlined,
  CoffeeOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { formatPrice } from '../../utils/formatters';

const { Text, Title } = Typography;

const BookingRoomCard = ({ room, showActions = true }) => {
  const navigate = useNavigate();
  const [isFavorite, setIsFavorite] = useState(false);

  // Get data from room object
  const images = room.images || [];
  const user = room.user || {};
  const address = room.address || {};
  
  // Get primary image or fallback
  const imageUrl = images[0]?.image_url || 
                   'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=500';

  const getLocationText = () => {
    const ward = address.ward?.name;
    const district = address.ward?.district?.name;
    const province = address.ward?.district?.province?.name;
    
    if (ward && district) {
      return `${ward}, ${district}`;
    }
    if (district && province) {
      return `${district}, ${province}`;
    }
    if (province) {
      return province;
    }
    return 'Việt Nam';
  };

  const handleViewDetail = () => {
    navigate(`/rooms/${room.id}`);
  };

  const handleFavoriteClick = (e) => {
    e.stopPropagation();
    setIsFavorite(!isFavorite);
  };

  const getRoomTypeColor = (type) => {
    const colors = {
      'Phòng trọ': 'blue',
      'Chung cư': 'green',
      'Nhà riêng': 'orange',
      'Khách sạn': 'purple',
      'Homestay': 'cyan'
    };
    return colors[type] || 'default';
  };

  return (
    <Card
      hoverable
      style={{
        borderRadius: '8px',
        overflow: 'hidden',
        border: '1px solid #e1e1e1',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        marginBottom: '16px'
      }}
      bodyStyle={{ padding: 0 }}
      onClick={handleViewDetail}
    >
      {/* Image Section */}
      <div style={{ position: 'relative' }}>
        <Image
          src={imageUrl}
          alt={room.title}
          style={{
            width: '100%',
            height: '200px',
            objectFit: 'cover'
          }}
          preview={false}
        />
        
        {/* Favorite Button */}
        <Button
          type="text"
          icon={isFavorite ? <HeartFilled /> : <HeartOutlined />}
          onClick={handleFavoriteClick}
          style={{
            position: 'absolute',
            top: '8px',
            right: '8px',
            color: isFavorite ? '#ff4757' : '#fff',
            backgroundColor: 'rgba(0,0,0,0.3)',
            border: 'none',
            borderRadius: '50%',
            width: '32px',
            height: '32px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        />

        {/* Room Type Tag */}
        <Tag
          color={getRoomTypeColor(room.room_type)}
          style={{
            position: 'absolute',
            top: '8px',
            left: '8px',
            fontWeight: '500',
            border: 'none'
          }}
        >
          {room.room_type}
        </Tag>
      </div>

      {/* Content Section */}
      <div style={{ padding: '16px' }}>
        {/* Location and Rating */}
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'flex-start',
          marginBottom: '8px'
        }}>
          <div style={{ flex: 1 }}>
            <Text style={{ 
              fontSize: '14px', 
              color: '#0071c2',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              gap: '4px'
            }}>
              <EnvironmentOutlined style={{ fontSize: '12px' }} />
              {getLocationText()}
            </Text>
          </div>
          
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: '4px',
            backgroundColor: '#003580',
            color: '#fff',
            padding: '2px 6px',
            borderRadius: '4px',
            fontSize: '12px',
            fontWeight: '600'
          }}>
            <span>8.5</span>
            <StarFilled style={{ fontSize: '10px' }} />
          </div>
        </div>

        {/* Title */}
        <Title 
          level={5} 
          style={{ 
            margin: '0 0 8px 0',
            fontSize: '16px',
            fontWeight: '600',
            color: '#003580',
            lineHeight: '1.3',
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden'
          }}
        >
          {room.title}
        </Title>

        {/* Host */}
        <Text style={{ 
          fontSize: '14px', 
          color: '#666',
          display: 'flex',
          alignItems: 'center',
          gap: '4px',
          marginBottom: '8px'
        }}>
          <UserOutlined style={{ fontSize: '12px' }} />
          Chủ nhà: {user.name || 'Ẩn danh'}
        </Text>

        {/* Amenities */}
        <div style={{ marginBottom: '12px' }}>
          <Space size={4}>
            <Tag icon={<WifiOutlined />} color="green" style={{ fontSize: '11px', padding: '2px 6px' }}>
              WiFi
            </Tag>
            <Tag icon={<CarOutlined />} color="blue" style={{ fontSize: '11px', padding: '2px 6px' }}>
              Đỗ xe
            </Tag>
            <Tag icon={<CoffeeOutlined />} color="orange" style={{ fontSize: '11px', padding: '2px 6px' }}>
              Bếp
            </Tag>
          </Space>
        </div>

        <Divider style={{ margin: '12px 0' }} />

        {/* Price and Booking */}
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center'
        }}>
          <div>
            <div style={{ display: 'flex', alignItems: 'baseline', gap: '4px' }}>
              <Text style={{ 
                fontSize: '18px', 
                fontWeight: '700', 
                color: '#003580'
              }}>
                {formatPrice(room.price)}
              </Text>
              <Text style={{ 
                fontSize: '14px', 
                color: '#666'
              }}>
                / tháng
              </Text>
            </div>
            <Text style={{ 
              fontSize: '12px', 
              color: '#666'
            }}>
              Đã bao gồm thuế và phí
            </Text>
          </div>
          
          <Button
            type="primary"
            onClick={(e) => {
              e.stopPropagation();
              handleViewDetail();
            }}
            style={{
              backgroundColor: '#0071c2',
              borderColor: '#0071c2',
              borderRadius: '4px',
              fontWeight: '600',
              height: '36px'
            }}
          >
            Xem chi tiết
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default BookingRoomCard;
