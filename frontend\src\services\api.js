import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    console.log('🔄 API Request:', {
      method: config.method?.toUpperCase(),
      url: config.url,
      baseURL: config.baseURL,
      fullURL: `${config.baseURL}${config.url}`,
      data: config.data
    });

    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      console.log('🔑 Token added to request');
    } else {
      console.log('⚠️ No token found in localStorage');
    }
    return config;
  },
  (error) => {
    console.error('❌ Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    console.log('✅ API Response:', {
      status: response.status,
      url: response.config.url,
      data: response.data
    });
    return response.data;
  },
  (error) => {
    console.error('❌ API Error:', {
      status: error.response?.status,
      url: error.config?.url,
      message: error.message,
      data: error.response?.data
    });

    if (error.response?.status === 401) {
      console.log('🔒 Unauthorized - redirecting to login');
      // Token expired or invalid
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error.response?.data || error.message);
  }
);

// Auth API
export const authAPI = {
  register: (userData) => api.post('/auth/register', userData),
  login: (credentials) => api.post('/auth/login', credentials),
  getProfile: () => api.get('/auth/profile'),
  updateProfile: (userData) => api.put('/auth/profile', userData),
  changePassword: (passwordData) => api.put('/auth/change-password', passwordData),
};

// Address API
export const addressAPI = {
  getProvinces: () => api.get('/addresses/provinces'),
  getDistricts: (provinceId) => api.get(`/addresses/provinces/${provinceId}/districts`),
  getWards: (districtId) => api.get(`/addresses/districts/${districtId}/wards`),
  createAddress: (addressData) => api.post('/addresses', addressData),
  getAddress: (id) => api.get(`/addresses/${id}`),
  searchAddresses: (params) => api.get('/addresses/search', { params }),
};

// Room API
export const roomAPI = {
  getAllRooms: (params) => api.get('/rooms', { params }),
  getRoomById: (id) => api.get(`/rooms/${id}`),
  createRoom: (roomData) => api.post('/rooms', roomData),
  updateRoom: (id, roomData) => api.put(`/rooms/${id}`, roomData),
  deleteRoom: (id) => api.delete(`/rooms/${id}`),
  getUserRooms: (params) => api.get('/rooms/user/my-rooms', { params }),
};

// Place API
export const placeAPI = {
  getAllPlaces: (params) => api.get('/places', { params }),
  getPlaceById: (id) => api.get(`/places/${id}`),
  createPlace: (placeData) => api.post('/places', placeData),
  updatePlace: (id, placeData) => api.put(`/places/${id}`, placeData),
  deletePlace: (id) => api.delete(`/places/${id}`),
  getRegions: () => api.get('/places/regions'),
};

// Review API
export const reviewAPI = {
  getAllReviews: (params) => api.get('/reviews', { params }),
  getReviewById: (id) => api.get(`/reviews/${id}`),
  createReview: (reviewData) => api.post('/reviews', reviewData),
  updateReview: (id, reviewData) => api.put(`/reviews/${id}`, reviewData),
  deleteReview: (id) => api.delete(`/reviews/${id}`),
  getReviewsByPlace: (placeId, params) => api.get(`/places/${placeId}/reviews`, { params }),
};

// Admin API
export const adminAPI = {
  // Dashboard
  getDashboardStats: () => api.get('/admin/dashboard'),

  // User Management
  getAllUsers: (params) => api.get('/admin/users', { params }),
  getUserById: (id) => api.get(`/admin/users/${id}`),
  updateUser: (id, userData) => api.put(`/admin/users/${id}`, userData),
  deleteUser: (id) => api.delete(`/admin/users/${id}`),

  // Review Management
  getAllReviews: (params) => api.get('/admin/reviews', { params }),
  deleteReview: (id) => api.delete(`/admin/reviews/${id}`),
};

// Upload API
export const uploadAPI = {
  uploadImages: (formData) => {
    return api.post('/upload/images', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  uploadSingle: (formData) => {
    return api.post('/upload/single', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
};

// Booking API
export const bookingAPI = {
  // Create a new booking
  createBooking: (bookingData) => {
    return api.post('/bookings', bookingData);
  },

  // Get user's bookings
  getUserBookings: (params = {}) => {
    return api.get('/bookings/my-bookings', { params });
  },

  // Get booking by ID
  getBookingById: (id) => {
    return api.get(`/bookings/${id}`);
  },

  // Cancel booking
  cancelBooking: (id, reason) => {
    return api.patch(`/bookings/${id}/cancel`, { cancelled_reason: reason });
  },

  // Check room availability
  checkAvailability: (params) => {
    return api.get('/bookings/check-availability', { params });
  }
};

export default api;
