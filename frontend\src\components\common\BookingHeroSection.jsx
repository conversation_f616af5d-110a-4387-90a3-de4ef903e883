import React from "react";
import { Typography, Space } from "antd";
import BookingSearchBar from "./BookingSearchBar";

const { Title, Text } = Typography;

const BookingHeroSection = ({ onSearch, loading, initialValues }) => {
  return (
    <div
      style={{
        background: "linear-gradient(135deg, #003580 0%, #0071c2 100%)",
        backgroundImage: `url('https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundBlendMode: "overlay",
        padding: "60px 24px 80px",
        position: "relative",
        minHeight: "400px",
      }}
    >
      {/* Overlay */}
      <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: "rgba(0, 0, 0, 0.3)",
          zIndex: 1,
        }}
      />

      {/* Content */}
      <div
        style={{
          position: "relative",
          zIndex: 2,
          maxWidth: "1020px",
          margin: "0 auto",
          textAlign: "center",
        }}
      >
        <Space direction="vertical" size="large" style={{ width: "100%" }}>
          <div>
            <Title
              level={1}
              style={{
                color: "#fff",
                fontSize: "48px",
                fontWeight: "700",
                marginBottom: "8px",
                lineHeight: "1.2",
              }}
            >
              Tìm chỗ nghỉ tiếp theo
            </Title>

            <Text
              style={{
                fontSize: "24px",
                color: "#fff",
                display: "block",
                marginBottom: "40px",
                fontWeight: "400",
              }}
            >
              Tìm ưu đãi khách sạn, chỗ nghỉ dạng nhà và nhiều hơn nữa...
            </Text>
          </div>

          {/* Search Bar */}
          <BookingSearchBar
            onSearch={onSearch}
            loading={loading}
            initialValues={initialValues}
          />

          {/* Additional Info */}
          <div style={{ marginTop: "32px" }}>
            <Text
              style={{
                fontSize: "16px",
                color: "rgba(255,255,255,0.9)",
                display: "block",
              }}
            >
              🏆 Được tin tưởng bởi hàng triệu khách hàng trên toàn thế giới
            </Text>
          </div>
        </Space>
      </div>
    </div>
  );
};

export default BookingHeroSection;
