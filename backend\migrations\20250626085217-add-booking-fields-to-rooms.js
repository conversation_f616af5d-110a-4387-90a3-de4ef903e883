'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Add booking-related fields to rooms table
    await queryInterface.addColumn('rooms', 'max_guests', {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 1,
      comment: 'Số khách tối đa'
    });

    await queryInterface.addColumn('rooms', 'min_stay_days', {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 1,
      comment: 'Số ngày thuê tối thiểu'
    });

    await queryInterface.addColumn('rooms', 'max_stay_days', {
      type: Sequelize.INTEGER,
      allowNull: true,
      comment: 'Số ngày thuê tối đa (null = không giới hạn)'
    });

    await queryInterface.addColumn('rooms', 'check_in_time', {
      type: Sequelize.TIME,
      allowNull: true,
      defaultValue: '14:00:00',
      comment: '<PERSON><PERSON><PERSON> nhận phòng'
    });

    await queryInterface.addColumn('rooms', 'check_out_time', {
      type: Sequelize.TIME,
      allowNull: true,
      defaultValue: '12:00:00',
      comment: 'Giờ trả phòng'
    });

    await queryInterface.addColumn('rooms', 'instant_book', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Cho phép đặt phòng ngay lập tức'
    });

    await queryInterface.addColumn('rooms', 'cancellation_policy', {
      type: Sequelize.ENUM('flexible', 'moderate', 'strict'),
      allowNull: false,
      defaultValue: 'moderate',
      comment: 'Chính sách hủy phòng'
    });

    await queryInterface.addColumn('rooms', 'house_rules', {
      type: Sequelize.TEXT,
      allowNull: true,
      comment: 'Nội quy nhà'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('rooms', 'max_guests');
    await queryInterface.removeColumn('rooms', 'min_stay_days');
    await queryInterface.removeColumn('rooms', 'max_stay_days');
    await queryInterface.removeColumn('rooms', 'check_in_time');
    await queryInterface.removeColumn('rooms', 'check_out_time');
    await queryInterface.removeColumn('rooms', 'instant_book');
    await queryInterface.removeColumn('rooms', 'cancellation_policy');
    await queryInterface.removeColumn('rooms', 'house_rules');
  }
};
