import React, { useState } from 'react';
import { Button, Space } from 'antd';
import {
  HomeOutlined,
  BuildingOutlined,
  BankOutlined,
  ShopOutlined,
  HeartOutlined,
  EnvironmentOutlined,
  StarOutlined,
  FireOutlined
} from '@ant-design/icons';

const CategoryFilter = ({ onCategoryChange, activeCategory }) => {
  const categories = [
    {
      key: 'all',
      label: 'Tất cả',
      icon: <HomeOutlined />,
      description: 'Tất cả loại chỗ ở'
    },
    {
      key: 'Phòng trọ',
      label: 'Phòng trọ',
      icon: <HomeOutlined />,
      description: 'Phòng trọ giá rẻ'
    },
    {
      key: '<PERSON> cư',
      label: '<PERSON> cư',
      icon: <BuildingOutlined />,
      description: '<PERSON><PERSON><PERSON> hộ chung cư'
    },
    {
      key: 'Nhà riêng',
      label: 'Nhà riêng',
      icon: <BankOutlined />,
      description: 'Nhà nguyên căn'
    },
    {
      key: '<PERSON>h<PERSON><PERSON> sạn',
      label: '<PERSON>h<PERSON><PERSON> sạn',
      icon: <ShopOutlined />,
      description: 'Phòng khách sạn'
    },
    {
      key: 'Homestay',
      label: 'Homestay',
      icon: <HeartOutlined />,
      description: 'Homestay ấm cúng'
    },
    {
      key: 'popular',
      label: 'Phổ biến',
      icon: <FireOutlined />,
      description: 'Được yêu thích nhất'
    },
    {
      key: 'top-rated',
      label: 'Đánh giá cao',
      icon: <StarOutlined />,
      description: 'Đánh giá tốt nhất'
    }
  ];

  const handleCategoryClick = (categoryKey) => {
    onCategoryChange(categoryKey === 'all' ? null : categoryKey);
  };

  return (
    <div style={{
      padding: '16px 24px',
      borderBottom: '1px solid #e8e8e8',
      backgroundColor: '#fff',
      overflowX: 'auto',
      whiteSpace: 'nowrap'
    }}>
      <div style={{
        maxWidth: '1760px',
        margin: '0 auto',
        display: 'flex',
        gap: '32px',
        alignItems: 'center'
      }}>
        {categories.map((category) => (
          <Button
            key={category.key}
            type="text"
            onClick={() => handleCategoryClick(category.key)}
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              height: 'auto',
              padding: '12px 8px',
              border: 'none',
              borderRadius: '8px',
              minWidth: '80px',
              color: activeCategory === category.key || 
                     (activeCategory === null && category.key === 'all') 
                     ? '#222' : '#717171',
              borderBottom: activeCategory === category.key || 
                           (activeCategory === null && category.key === 'all')
                           ? '2px solid #222' : '2px solid transparent',
              backgroundColor: 'transparent'
            }}
          >
            <div style={{ 
              fontSize: '24px', 
              marginBottom: '4px',
              opacity: activeCategory === category.key || 
                       (activeCategory === null && category.key === 'all') 
                       ? 1 : 0.6
            }}>
              {category.icon}
            </div>
            <div style={{ 
              fontSize: '12px', 
              fontWeight: activeCategory === category.key || 
                          (activeCategory === null && category.key === 'all') 
                          ? '600' : '400',
              textAlign: 'center',
              lineHeight: '16px'
            }}>
              {category.label}
            </div>
          </Button>
        ))}
      </div>
    </div>
  );
};

export default CategoryFilter;
