const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const RoomAvailability = sequelize.define('RoomAvailability', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true
  },
  room_id: {
    type: DataTypes.BIGINT,
    allowNull: false,
    references: {
      model: 'rooms',
      key: 'id'
    }
  },
  date: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    validate: {
      isDate: true
    }
  },
  is_available: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  price_override: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    validate: {
      min: 0
    }
  },
  min_stay_override: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 1
    }
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'room_availability',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      unique: true,
      fields: ['room_id', 'date'],
      name: 'unique_room_date'
    },
    {
      fields: ['room_id']
    },
    {
      fields: ['date']
    },
    {
      fields: ['is_available']
    }
  ]
});

module.exports = RoomAvailability;
