import React, { useState } from "react";
import {
  Input,
  Select,
  Button,
  DatePicker,
  Card,
  Row,
  Col,
  InputNumber,
} from "antd";
import {
  SearchOutlined,
  UserOutlined,
  EnvironmentOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";

const { Option } = Select;
const { RangePicker } = DatePicker;

const BookingSearchBar = ({ onSearch, loading, style, initialValues = {} }) => {
  const [searchForm, setSearchForm] = useState({
    search: initialValues.search || "",
    check_in: initialValues.check_in || null,
    check_out: initialValues.check_out || null,
    guests: initialValues.guests || 1,
    room_type: initialValues.room_type || undefined,
  });

  const roomTypes = [
    "Phòng trọ",
    "Chung cư",
    "Nhà riêng",
    "Khách sạn",
    "Homestay",
  ];

  const handleDateChange = (dates) => {
    setSearchForm((prev) => ({
      ...prev,
      check_in: dates ? dates[0] : null,
      check_out: dates ? dates[1] : null,
    }));
  };

  // Convert initial date values to dayjs objects if they're strings
  const getDateValue = (dateValue) => {
    if (!dateValue) return null;
    if (typeof dateValue === "string") {
      return dayjs(dateValue);
    }
    return dateValue;
  };

  const handleInputChange = (field, value) => {
    setSearchForm((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSearch = () => {
    const cleanFilters = Object.fromEntries(
      Object.entries(searchForm).filter(
        ([_, value]) => value !== undefined && value !== ""
      )
    );
    onSearch(cleanFilters);
  };

  return (
    <div
      style={{
        padding: "0",
        backgroundColor: "transparent",
        ...style,
      }}
    >
      <Card
        style={{
          backgroundColor: "#febb02",
          border: "none",
          borderRadius: "8px",
          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
          margin: "0 auto",
          maxWidth: "1020px",
        }}
        styles={{ body: { padding: "24px" } }}
      >
        <Row gutter={[8, 16]} align="middle">
          {/* Location Search */}
          <Col xs={24} sm={12} md={8} lg={8}>
            <div style={{ position: "relative" }}>
              <div
                style={{
                  position: "absolute",
                  left: "12px",
                  top: "50%",
                  transform: "translateY(-50%)",
                  zIndex: 1,
                  color: "#666",
                }}
              >
                <EnvironmentOutlined />
              </div>
              <Input
                placeholder="Bạn muốn đi đâu?"
                value={searchForm.search}
                onChange={(e) => handleInputChange("search", e.target.value)}
                style={{
                  height: "48px",
                  paddingLeft: "40px",
                  fontSize: "14px",
                  fontWeight: "500",
                  border: "3px solid #febb02",
                  borderRadius: "3px",
                }}
                onFocus={(e) => {
                  e.target.style.border = "3px solid #0071c2";
                }}
                onBlur={(e) => {
                  e.target.style.border = "3px solid #febb02";
                }}
              />
            </div>
          </Col>

          {/* Date Range */}
          <Col xs={24} sm={12} md={8} lg={8}>
            <RangePicker
              placeholder={["Ngày nhận phòng", "Ngày trả phòng"]}
              value={[
                getDateValue(searchForm.check_in),
                getDateValue(searchForm.check_out),
              ]}
              onChange={handleDateChange}
              style={{
                height: "48px",
                width: "100%",
                fontSize: "14px",
                fontWeight: "500",
                border: "3px solid #febb02",
                borderRadius: "3px",
              }}
              format="DD/MM/YYYY"
              disabledDate={(current) => {
                // Disable dates before today
                return current && current < dayjs().startOf("day");
              }}
            />
          </Col>

          {/* Guests and Room Type */}
          <Col xs={24} sm={12} md={4} lg={4}>
            <div style={{ position: "relative" }}>
              <div
                style={{
                  position: "absolute",
                  left: "12px",
                  top: "50%",
                  transform: "translateY(-50%)",
                  zIndex: 1,
                  color: "#666",
                }}
              >
                <UserOutlined />
              </div>
              <InputNumber
                placeholder="Số khách"
                min={1}
                max={20}
                value={searchForm.guests}
                onChange={(value) => handleInputChange("guests", value)}
                style={{
                  height: "48px",
                  width: "100%",
                  paddingLeft: "40px",
                  fontSize: "14px",
                  fontWeight: "500",
                  border: "3px solid #febb02",
                  borderRadius: "3px",
                }}
              />
            </div>
          </Col>

          {/* Search Button */}
          <Col xs={24} sm={12} md={4} lg={4}>
            <Button
              type="primary"
              icon={<SearchOutlined />}
              onClick={handleSearch}
              loading={loading}
              style={{
                backgroundColor: "#0071c2",
                borderColor: "#0071c2",
                height: "48px",
                width: "100%",
                fontSize: "16px",
                fontWeight: "600",
                borderRadius: "3px",
              }}
            >
              Tìm kiếm
            </Button>
          </Col>
        </Row>

        {/* Room Type Filter */}
        <Row gutter={[8, 16]} style={{ marginTop: "16px" }}>
          <Col xs={24} sm={12} md={8}>
            <Select
              placeholder="Loại chỗ ở"
              value={searchForm.room_type}
              onChange={(value) => handleInputChange("room_type", value)}
              allowClear
              style={{ width: "100%", height: "40px" }}
            >
              {roomTypes.map((type) => (
                <Option key={type} value={type}>
                  {type}
                </Option>
              ))}
            </Select>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default BookingSearchBar;
