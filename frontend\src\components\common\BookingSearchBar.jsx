import React, { useState, useEffect } from 'react';
import { Input, Select, Button, DatePicker, Space, Card, Row, Col } from 'antd';
import { SearchOutlined, CalendarOutlined, UserOutlined, EnvironmentOutlined } from '@ant-design/icons';
import { addressAPI } from '../../services/api';

const { Option } = Select;
const { RangePicker } = DatePicker;

const BookingSearchBar = ({ onSearch, loading, style, initialValues = {} }) => {
  const [searchForm, setSearchForm] = useState({
    search: initialValues.search || '',
    province_id: initialValues.province_id || undefined,
    district_id: initialValues.district_id || undefined,
    ward_id: initialValues.ward_id || undefined,
    room_type: initialValues.room_type || undefined,
    min_price: initialValues.min_price || undefined,
    max_price: initialValues.max_price || undefined
  });

  const [provinces, setProvinces] = useState([]);
  const [districts, setDistricts] = useState([]);
  const [wards, setWards] = useState([]);
  const [loadingAddress, setLoadingAddress] = useState(false);

  const roomTypes = [
    'Phòng trọ',
    'Chung cư', 
    'Nhà riêng',
    'Khách sạn',
    'Homestay'
  ];

  useEffect(() => {
    fetchProvinces();
  }, []);

  const fetchProvinces = async () => {
    try {
      setLoadingAddress(true);
      const response = await addressAPI.getProvinces();
      if (response.success) {
        setProvinces(response.data.provinces);
      }
    } catch (error) {
      console.error('Error fetching provinces:', error);
    } finally {
      setLoadingAddress(false);
    }
  };

  const fetchDistricts = async (provinceId) => {
    try {
      setLoadingAddress(true);
      const response = await addressAPI.getDistricts(provinceId);
      if (response.success) {
        setDistricts(response.data.districts);
        return response.data.districts;
      }
    } catch (error) {
      console.error('Error fetching districts:', error);
    } finally {
      setLoadingAddress(false);
    }
    return [];
  };

  const fetchWards = async (districtId) => {
    try {
      setLoadingAddress(true);
      const response = await addressAPI.getWards(districtId);
      if (response.success) {
        setWards(response.data.wards);
        return response.data.wards;
      }
    } catch (error) {
      console.error('Error fetching wards:', error);
    } finally {
      setLoadingAddress(false);
    }
    return [];
  };

  const handleProvinceChange = (value) => {
    setSearchForm(prev => ({
      ...prev,
      province_id: value,
      district_id: undefined,
      ward_id: undefined
    }));
    setDistricts([]);
    setWards([]);

    if (value) {
      fetchDistricts(value);
    }
  };

  const handleDistrictChange = (value) => {
    setSearchForm(prev => ({
      ...prev,
      district_id: value,
      ward_id: undefined
    }));
    setWards([]);

    if (value) {
      fetchWards(value);
    }
  };

  const handleInputChange = (field, value) => {
    setSearchForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSearch = () => {
    const cleanFilters = Object.fromEntries(
      Object.entries(searchForm).filter(([_, value]) => value !== undefined && value !== '')
    );
    onSearch(cleanFilters);
  };

  const getLocationText = () => {
    if (searchForm.search) return searchForm.search;
    
    const province = provinces.find(p => p.id === searchForm.province_id);
    const district = districts.find(d => d.id === searchForm.district_id);
    const ward = wards.find(w => w.id === searchForm.ward_id);
    
    if (ward) return `${ward.name}, ${district?.name}, ${province?.name}`;
    if (district) return `${district.name}, ${province?.name}`;
    if (province) return province.name;
    
    return 'Bạn muốn đi đâu?';
  };

  return (
    <div style={{ 
      padding: '0',
      backgroundColor: 'transparent',
      ...style 
    }}>
      <Card
        style={{
          backgroundColor: '#febb02',
          border: 'none',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
          margin: '0 auto',
          maxWidth: '1020px'
        }}
        bodyStyle={{ padding: '24px' }}
      >
        <Row gutter={[8, 16]} align="middle">
          {/* Location */}
          <Col xs={24} sm={12} md={6} lg={6}>
            <div style={{ position: 'relative' }}>
              <div style={{
                position: 'absolute',
                left: '12px',
                top: '50%',
                transform: 'translateY(-50%)',
                zIndex: 1,
                color: '#666'
              }}>
                <EnvironmentOutlined />
              </div>
              <Input
                placeholder="Bạn muốn đi đâu?"
                value={searchForm.search}
                onChange={(e) => handleInputChange('search', e.target.value)}
                style={{
                  height: '48px',
                  paddingLeft: '40px',
                  fontSize: '14px',
                  fontWeight: '500',
                  border: '3px solid #febb02',
                  borderRadius: '3px'
                }}
                onFocus={(e) => {
                  e.target.style.border = '3px solid #0071c2';
                }}
                onBlur={(e) => {
                  e.target.style.border = '3px solid #febb02';
                }}
              />
            </div>
          </Col>

          {/* Check-in Date */}
          <Col xs={24} sm={12} md={4} lg={4}>
            <div style={{ position: 'relative' }}>
              <div style={{
                position: 'absolute',
                left: '12px',
                top: '50%',
                transform: 'translateY(-50%)',
                zIndex: 1,
                color: '#666'
              }}>
                <CalendarOutlined />
              </div>
              <Input
                placeholder="Ngày nhận phòng"
                style={{
                  height: '48px',
                  paddingLeft: '40px',
                  fontSize: '14px',
                  fontWeight: '500',
                  border: '3px solid #febb02',
                  borderRadius: '3px'
                }}
                onFocus={(e) => {
                  e.target.style.border = '3px solid #0071c2';
                }}
                onBlur={(e) => {
                  e.target.style.border = '3px solid #febb02';
                }}
              />
            </div>
          </Col>

          {/* Check-out Date */}
          <Col xs={24} sm={12} md={4} lg={4}>
            <div style={{ position: 'relative' }}>
              <div style={{
                position: 'absolute',
                left: '12px',
                top: '50%',
                transform: 'translateY(-50%)',
                zIndex: 1,
                color: '#666'
              }}>
                <CalendarOutlined />
              </div>
              <Input
                placeholder="Ngày trả phòng"
                style={{
                  height: '48px',
                  paddingLeft: '40px',
                  fontSize: '14px',
                  fontWeight: '500',
                  border: '3px solid #febb02',
                  borderRadius: '3px'
                }}
                onFocus={(e) => {
                  e.target.style.border = '3px solid #0071c2';
                }}
                onBlur={(e) => {
                  e.target.style.border = '3px solid #febb02';
                }}
              />
            </div>
          </Col>

          {/* Guests */}
          <Col xs={24} sm={12} md={4} lg={4}>
            <div style={{ position: 'relative' }}>
              <div style={{
                position: 'absolute',
                left: '12px',
                top: '50%',
                transform: 'translateY(-50%)',
                zIndex: 1,
                color: '#666'
              }}>
                <UserOutlined />
              </div>
              <Select
                placeholder="Số khách"
                style={{
                  width: '100%',
                  height: '48px'
                }}
                value={searchForm.room_type}
                onChange={(value) => handleInputChange('room_type', value)}
                allowClear
              >
                {roomTypes.map(type => (
                  <Option key={type} value={type}>
                    {type}
                  </Option>
                ))}
              </Select>
            </div>
          </Col>

          {/* Search Button */}
          <Col xs={24} sm={12} md={6} lg={6}>
            <Button
              type="primary"
              icon={<SearchOutlined />}
              onClick={handleSearch}
              loading={loading}
              style={{
                backgroundColor: '#0071c2',
                borderColor: '#0071c2',
                height: '48px',
                width: '100%',
                fontSize: '16px',
                fontWeight: '600',
                borderRadius: '3px'
              }}
            >
              Tìm kiếm
            </Button>
          </Col>
        </Row>

        {/* Advanced Filters Row */}
        <Row gutter={[8, 16]} style={{ marginTop: '16px' }}>
          <Col xs={24} sm={8} md={6}>
            <Select
              placeholder="Tỉnh/Thành phố"
              value={searchForm.province_id}
              onChange={handleProvinceChange}
              loading={loadingAddress}
              allowClear
              showSearch
              style={{ width: '100%', height: '40px' }}
            >
              {provinces.map(province => (
                <Option key={province.id} value={province.id}>
                  {province.name}
                </Option>
              ))}
            </Select>
          </Col>

          <Col xs={24} sm={8} md={6}>
            <Select
              placeholder="Quận/Huyện"
              value={searchForm.district_id}
              onChange={handleDistrictChange}
              loading={loadingAddress}
              allowClear
              showSearch
              style={{ width: '100%', height: '40px' }}
              disabled={!searchForm.province_id}
            >
              {districts.map(district => (
                <Option key={district.id} value={district.id}>
                  {district.name}
                </Option>
              ))}
            </Select>
          </Col>

          <Col xs={24} sm={8} md={6}>
            <Select
              placeholder="Phường/Xã"
              value={searchForm.ward_id}
              onChange={(value) => handleInputChange('ward_id', value)}
              loading={loadingAddress}
              allowClear
              showSearch
              style={{ width: '100%', height: '40px' }}
              disabled={!searchForm.district_id}
            >
              {wards.map(ward => (
                <Option key={ward.id} value={ward.id}>
                  {ward.name}
                </Option>
              ))}
            </Select>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default BookingSearchBar;
