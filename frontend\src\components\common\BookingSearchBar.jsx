import React, { useState, useEffect } from "react";
import { Input, Select, But<PERSON>, Card, Row, Col } from "antd";
import { SearchOutlined, EnvironmentOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import { addressAPI } from "../../services/api";

const { Option } = Select;

const BookingSearchBar = ({ onSearch, loading, style, initialValues = {} }) => {
  const navigate = useNavigate();
  const [searchForm, setSearchForm] = useState({
    search: initialValues.search || "",
    province_id: initialValues.province_id || undefined,
    district_id: initialValues.district_id || undefined,
    ward_id: initialValues.ward_id || undefined,
  });

  // Address data
  const [provinces, setProvinces] = useState([]);
  const [districts, setDistricts] = useState([]);
  const [wards, setWards] = useState([]);
  const [loadingAddress, setLoadingAddress] = useState(false);

  useEffect(() => {
    fetchProvinces();
  }, []);

  const fetchProvinces = async () => {
    try {
      setLoadingAddress(true);
      const response = await addressAPI.getProvinces();
      if (response.success) {
        setProvinces(response.data.provinces);
      }
    } catch (error) {
      console.error("Error fetching provinces:", error);
    } finally {
      setLoadingAddress(false);
    }
  };

  const fetchDistricts = async (provinceId) => {
    try {
      setLoadingAddress(true);
      const response = await addressAPI.getDistricts(provinceId);
      if (response.success) {
        setDistricts(response.data.districts);
      }
    } catch (error) {
      console.error("Error fetching districts:", error);
    } finally {
      setLoadingAddress(false);
    }
  };

  const fetchWards = async (districtId) => {
    try {
      setLoadingAddress(true);
      const response = await addressAPI.getWards(districtId);
      if (response.success) {
        setWards(response.data.wards);
      }
    } catch (error) {
      console.error("Error fetching wards:", error);
    } finally {
      setLoadingAddress(false);
    }
  };

  const handleInputChange = (field, value) => {
    setSearchForm((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Handle cascading dropdowns
    if (field === "province_id") {
      setSearchForm((prev) => ({
        ...prev,
        district_id: undefined,
        ward_id: undefined,
      }));
      setDistricts([]);
      setWards([]);
      if (value) {
        fetchDistricts(value);
      }
    } else if (field === "district_id") {
      setSearchForm((prev) => ({
        ...prev,
        ward_id: undefined,
      }));
      setWards([]);
      if (value) {
        fetchWards(value);
      }
    }
  };

  const handleSearch = () => {
    const cleanFilters = Object.fromEntries(
      Object.entries(searchForm).filter(
        ([_, value]) => value !== undefined && value !== ""
      )
    );

    // If onSearch is provided (for Rooms page), use it
    if (onSearch) {
      onSearch(cleanFilters);
    } else {
      // If no onSearch (for Home page), navigate to BookingRooms with search params
      const searchParams = new URLSearchParams();
      Object.entries(cleanFilters).forEach(([key, value]) => {
        if (value !== null && value !== undefined && value !== "") {
          searchParams.append(key, value.toString());
        }
      });
      navigate(`/booking-rooms?${searchParams.toString()}`);
    }
  };

  return (
    <div
      style={{
        padding: "0",
        backgroundColor: "transparent",
        ...style,
      }}
    >
      <Card
        style={{
          backgroundColor: "#febb02",
          border: "none",
          borderRadius: "8px",
          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
          margin: "0 auto",
          maxWidth: "1020px",
        }}
        styles={{ body: { padding: "24px" } }}
      >
        <Row gutter={[8, 16]} align="middle">
          {/* Location Search */}
          <Col xs={24} sm={12} md={6} lg={6}>
            <div style={{ position: "relative" }}>
              <div
                style={{
                  position: "absolute",
                  left: "12px",
                  top: "50%",
                  transform: "translateY(-50%)",
                  zIndex: 1,
                  color: "#666",
                }}
              >
                <EnvironmentOutlined />
              </div>
              <Input
                placeholder="Tìm theo tên phòng, địa chỉ..."
                value={searchForm.search}
                onChange={(e) => handleInputChange("search", e.target.value)}
                style={{
                  height: "48px",
                  paddingLeft: "40px",
                  fontSize: "14px",
                  fontWeight: "500",
                  border: "3px solid #febb02",
                  borderRadius: "3px",
                }}
                onFocus={(e) => {
                  e.target.style.border = "3px solid #0071c2";
                }}
                onBlur={(e) => {
                  e.target.style.border = "3px solid #febb02";
                }}
                onPressEnter={handleSearch}
              />
            </div>
          </Col>

          {/* Province */}
          <Col xs={24} sm={8} md={4} lg={4}>
            <Select
              placeholder="Tỉnh/Thành phố"
              value={searchForm.province_id}
              onChange={(value) => handleInputChange("province_id", value)}
              allowClear
              style={{
                width: "100%",
                height: "48px",
              }}
              loading={loadingAddress}
            >
              {provinces.map((province) => (
                <Option key={province.id} value={province.id}>
                  {province.name}
                </Option>
              ))}
            </Select>
          </Col>

          {/* District */}
          <Col xs={24} sm={8} md={4} lg={4}>
            <Select
              placeholder="Quận/Huyện"
              value={searchForm.district_id}
              onChange={(value) => handleInputChange("district_id", value)}
              allowClear
              style={{
                width: "100%",
                height: "48px",
              }}
              disabled={!searchForm.province_id}
              loading={loadingAddress}
            >
              {districts.map((district) => (
                <Option key={district.id} value={district.id}>
                  {district.name}
                </Option>
              ))}
            </Select>
          </Col>

          {/* Ward */}
          <Col xs={24} sm={8} md={4} lg={4}>
            <Select
              placeholder="Phường/Xã"
              value={searchForm.ward_id}
              onChange={(value) => handleInputChange("ward_id", value)}
              allowClear
              style={{
                width: "100%",
                height: "48px",
              }}
              disabled={!searchForm.district_id}
              loading={loadingAddress}
            >
              {wards.map((ward) => (
                <Option key={ward.id} value={ward.id}>
                  {ward.name}
                </Option>
              ))}
            </Select>
          </Col>

          {/* Search Button */}
          <Col xs={24} sm={12} md={6} lg={6}>
            <Button
              type="primary"
              icon={<SearchOutlined />}
              onClick={handleSearch}
              loading={loading}
              style={{
                backgroundColor: "#0071c2",
                borderColor: "#0071c2",
                height: "48px",
                width: "100%",
                fontSize: "16px",
                fontWeight: "600",
                borderRadius: "3px",
              }}
            >
              Tìm kiếm
            </Button>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default BookingSearchBar;
