'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('bookings', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      room_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'rooms',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      check_in_date: {
        type: Sequelize.DATEONLY,
        allowNull: false,
        comment: 'Ngày nhận phòng'
      },
      check_out_date: {
        type: Sequelize.DATEONLY,
        allowNull: false,
        comment: '<PERSON><PERSON><PERSON> trả phòng'
      },
      guests: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 1,
        comment: '<PERSON><PERSON> khách'
      },
      total_price: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
        comment: 'Tổng giá tiền'
      },
      status: {
        type: Sequelize.ENUM('pending', 'confirmed', 'cancelled', 'completed'),
        allowNull: false,
        defaultValue: 'pending',
        comment: 'Trạng thái đặt phòng'
      },
      payment_status: {
        type: Sequelize.ENUM('pending', 'paid', 'refunded'),
        allowNull: false,
        defaultValue: 'pending',
        comment: 'Trạng thái thanh toán'
      },
      payment_method: {
        type: Sequelize.ENUM('cash', 'bank_transfer', 'credit_card', 'e_wallet'),
        allowNull: true,
        comment: 'Phương thức thanh toán'
      },
      special_requests: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Yêu cầu đặc biệt'
      },
      guest_name: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: 'Tên khách hàng'
      },
      guest_phone: {
        type: Sequelize.STRING(20),
        allowNull: false,
        comment: 'Số điện thoại khách hàng'
      },
      guest_email: {
        type: Sequelize.STRING(100),
        allowNull: true,
        comment: 'Email khách hàng'
      },
      cancelled_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'Thời gian hủy'
      },
      cancelled_reason: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Lý do hủy'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // Add indexes for better performance
    await queryInterface.addIndex('bookings', ['room_id']);
    await queryInterface.addIndex('bookings', ['user_id']);
    await queryInterface.addIndex('bookings', ['check_in_date', 'check_out_date']);
    await queryInterface.addIndex('bookings', ['status']);
    await queryInterface.addIndex('bookings', ['created_at']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('bookings');
  }
};
