const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Room = sequelize.define('Room', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true
  },
  user_id: {
    type: DataTypes.BIGINT,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  address_id: {
    type: DataTypes.BIGINT,
    allowNull: false,
    references: {
      model: 'addresses',
      key: 'id'
    }
  },
  title: {
    type: DataTypes.STRING(255),
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [5, 255]
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  price: {
    type: DataTypes.DECIMAL(12, 2),
    allowNull: false,
    validate: {
      min: 0
    }
  },
  area: {
    type: DataTypes.FLOAT,
    allowNull: true,
    validate: {
      min: 0
    }
  },
  room_type: {
    type: DataTypes.ENUM('<PERSON>òng trọ', '<PERSON> cư', '<PERSON><PERSON>à riêng', '<PERSON>h<PERSON>ch sạn', 'Homestay'),
    defaultValue: 'Phòng trọ',
    allowNull: false
  },
  // Booking-related fields
  max_guests: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1,
    validate: {
      min: 1,
      max: 20
    }
  },
  min_stay_days: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1,
    validate: {
      min: 1
    }
  },
  max_stay_days: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 1
    }
  },
  check_in_time: {
    type: DataTypes.TIME,
    allowNull: true,
    defaultValue: '14:00:00'
  },
  check_out_time: {
    type: DataTypes.TIME,
    allowNull: true,
    defaultValue: '12:00:00'
  },
  instant_book: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  cancellation_policy: {
    type: DataTypes.ENUM('flexible', 'moderate', 'strict'),
    allowNull: false,
    defaultValue: 'moderate'
  },
  house_rules: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'rooms',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: false
});

module.exports = Room;
