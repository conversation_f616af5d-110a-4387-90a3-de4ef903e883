import React from "react";
import { Layout as AntLayout } from "antd";
import Header from "./Header";
import Footer from "./Footer";

const { Content } = AntLayout;

const Layout = ({ children }) => {
  return (
    <AntLayout
      style={{ minHeight: "100vh", display: "flex", flexDirection: "column" }}
    >
      <Header />
      <Content
        style={{
          padding: "0",
          backgroundColor: "#fff",
          marginTop: "80px", // Bù cho Header cố định (80px)
          flex: 1, // Chiếm toàn bộ không gian còn lại
        }}
      >
        {children}
      </Content>
      <Footer />
    </AntLayout>
  );
};

export default Layout;
