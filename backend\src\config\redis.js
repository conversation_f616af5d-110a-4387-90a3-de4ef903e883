// Simple in-memory cache for demo purposes
// In production, use Redis or other caching solutions
require('dotenv').config();

class SimpleCache {
  constructor() {
    this.cache = new Map();
    this.timers = new Map();
    console.log('✅ Simple in-memory cache initialized');
  }

  connect() {
    // Mock connection for compatibility
    return Promise.resolve();
  }

  quit() {
    this.cache.clear();
    this.timers.forEach(timer => clearTimeout(timer));
    this.timers.clear();
    console.log('⚠️ Cache cleared');
  }
}

const redisClient = new SimpleCache();

// Helper functions for caching
const cache = {
  get: async (key) => {
    try {
      const item = redisClient.cache.get(key);
      if (item && item.expiry > Date.now()) {
        return item.data;
      } else if (item) {
        redisClient.cache.delete(key);
        if (redisClient.timers.has(key)) {
          clearTimeout(redisClient.timers.get(key));
          redisClient.timers.delete(key);
        }
      }
      return null;
    } catch (error) {
      console.error('Cache GET error:', error);
      return null;
    }
  },

  set: async (key, data, expireInSeconds = 3600) => {
    try {
      const expiry = Date.now() + (expireInSeconds * 1000);
      redisClient.cache.set(key, { data, expiry });

      // Set timer to auto-delete
      if (redisClient.timers.has(key)) {
        clearTimeout(redisClient.timers.get(key));
      }
      const timer = setTimeout(() => {
        redisClient.cache.delete(key);
        redisClient.timers.delete(key);
      }, expireInSeconds * 1000);
      redisClient.timers.set(key, timer);

      return true;
    } catch (error) {
      console.error('Cache SET error:', error);
      return false;
    }
  },

  del: async (key) => {
    try {
      // Support wildcard deletion
      if (key.includes('*')) {
        const pattern = key.replace(/\*/g, '');
        const keysToDelete = [];

        for (const cacheKey of redisClient.cache.keys()) {
          if (cacheKey.startsWith(pattern)) {
            keysToDelete.push(cacheKey);
          }
        }

        keysToDelete.forEach(keyToDelete => {
          redisClient.cache.delete(keyToDelete);
          if (redisClient.timers.has(keyToDelete)) {
            clearTimeout(redisClient.timers.get(keyToDelete));
            redisClient.timers.delete(keyToDelete);
          }
        });

        console.log(`🗑️ Deleted ${keysToDelete.length} cache keys matching pattern: ${key}`);
        return true;
      } else {
        // Single key deletion
        redisClient.cache.delete(key);
        if (redisClient.timers.has(key)) {
          clearTimeout(redisClient.timers.get(key));
          redisClient.timers.delete(key);
        }
        return true;
      }
    } catch (error) {
      console.error('Cache DEL error:', error);
      return false;
    }
  },

  flush: async () => {
    try {
      redisClient.cache.clear();
      redisClient.timers.forEach(timer => clearTimeout(timer));
      redisClient.timers.clear();
      return true;
    } catch (error) {
      console.error('Cache FLUSH error:', error);
      return false;
    }
  }
};

module.exports = { redisClient, cache };
