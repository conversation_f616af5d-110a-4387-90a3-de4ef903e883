import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Row,
  Col,
  Typography,
  Button,
  Form,
  Input,
  DatePicker,
  InputNumber,
  message,
  Spin,
  Divider,
  Space,
  Tag
} from 'antd';
import {
  CalendarOutlined,
  UserOutlined,
  PhoneOutlined,
  MailOutlined,
  HomeOutlined
} from '@ant-design/icons';
import Layout from '../components/layout/Layout';
import { roomAPI, bookingAPI } from '../services/api';
import { formatPrice } from '../utils/formatters';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { RangePicker } = DatePicker;

const BookingPage = () => {
  const { roomId } = useParams();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  
  const [room, setRoom] = useState(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [dates, setDates] = useState([null, null]);
  const [guests, setGuests] = useState(1);
  const [totalPrice, setTotalPrice] = useState(0);

  useEffect(() => {
    fetchRoomDetails();
  }, [roomId]);

  useEffect(() => {
    calculateTotalPrice();
  }, [dates, room]);

  const fetchRoomDetails = async () => {
    try {
      setLoading(true);
      const response = await roomAPI.getRoomById(roomId);
      if (response.data.success) {
        setRoom(response.data.data.room);
      } else {
        message.error('Không thể tải thông tin phòng');
        navigate('/');
      }
    } catch (error) {
      console.error('Error fetching room:', error);
      message.error('Lỗi khi tải thông tin phòng');
      navigate('/');
    } finally {
      setLoading(false);
    }
  };

  const calculateTotalPrice = () => {
    if (dates[0] && dates[1] && room) {
      const checkIn = dayjs(dates[0]);
      const checkOut = dayjs(dates[1]);
      const nights = checkOut.diff(checkIn, 'day');
      setTotalPrice(room.price * nights);
    } else {
      setTotalPrice(0);
    }
  };

  const handleDateChange = (selectedDates) => {
    setDates(selectedDates || [null, null]);
  };

  const handleSubmit = async (values) => {
    try {
      setSubmitting(true);

      if (!dates[0] || !dates[1]) {
        message.error('Vui lòng chọn ngày nhận và trả phòng');
        return;
      }

      const bookingData = {
        room_id: parseInt(roomId),
        check_in_date: dates[0].format('YYYY-MM-DD'),
        check_out_date: dates[1].format('YYYY-MM-DD'),
        guests: guests,
        guest_name: values.guest_name,
        guest_phone: values.guest_phone,
        guest_email: values.guest_email,
        special_requests: values.special_requests
      };

      console.log('Booking data:', bookingData);

      const response = await bookingAPI.createBooking(bookingData);
      
      if (response.data.success) {
        message.success('Đặt phòng thành công!');
        navigate('/my-bookings');
      } else {
        message.error(response.data.message || 'Đặt phòng thất bại');
      }
    } catch (error) {
      console.error('Booking error:', error);
      const errorMessage = error.response?.data?.message || 'Lỗi khi đặt phòng';
      message.error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <Layout>
        <div style={{ textAlign: 'center', padding: '100px 0' }}>
          <Spin size="large" />
        </div>
      </Layout>
    );
  }

  if (!room) {
    return (
      <Layout>
        <div style={{ textAlign: 'center', padding: '100px 0' }}>
          <Title level={3}>Không tìm thấy phòng</Title>
        </div>
      </Layout>
    );
  }

  const nights = dates[0] && dates[1] ? dayjs(dates[1]).diff(dayjs(dates[0]), 'day') : 0;

  return (
    <Layout>
      <div style={{ 
        maxWidth: '1200px', 
        margin: '0 auto', 
        padding: '24px',
        backgroundColor: '#f5f5f5',
        minHeight: 'calc(100vh - 70px)'
      }}>
        <Row gutter={[24, 24]}>
          {/* Room Information */}
          <Col xs={24} lg={14}>
            <Card style={{ marginBottom: '24px' }}>
              <Space direction="vertical" size="large" style={{ width: '100%' }}>
                <div>
                  <Tag color="blue" style={{ marginBottom: '8px' }}>
                    {room.room_type}
                  </Tag>
                  <Title level={2} style={{ margin: 0, color: '#003580' }}>
                    {room.title}
                  </Title>
                  <Text style={{ fontSize: '16px', color: '#666' }}>
                    <HomeOutlined /> {room.address?.ward?.district?.province?.name}
                  </Text>
                </div>

                <div>
                  <Title level={4}>Mô tả</Title>
                  <Text>{room.description}</Text>
                </div>

                <Row gutter={[16, 16]}>
                  <Col span={12}>
                    <Text strong>Diện tích: </Text>
                    <Text>{room.area} m²</Text>
                  </Col>
                  <Col span={12}>
                    <Text strong>Số khách tối đa: </Text>
                    <Text>{room.max_guests} người</Text>
                  </Col>
                  <Col span={12}>
                    <Text strong>Giờ nhận phòng: </Text>
                    <Text>{room.check_in_time || '14:00'}</Text>
                  </Col>
                  <Col span={12}>
                    <Text strong>Giờ trả phòng: </Text>
                    <Text>{room.check_out_time || '12:00'}</Text>
                  </Col>
                </Row>

                <div>
                  <Title level={4}>Giá phòng</Title>
                  <Text style={{ fontSize: '24px', fontWeight: 'bold', color: '#003580' }}>
                    {formatPrice(room.price)} / đêm
                  </Text>
                </div>
              </Space>
            </Card>
          </Col>

          {/* Booking Form */}
          <Col xs={24} lg={10}>
            <Card 
              title={
                <Title level={3} style={{ margin: 0, color: '#003580' }}>
                  <CalendarOutlined /> Đặt phòng
                </Title>
              }
              style={{ position: 'sticky', top: '24px' }}
            >
              <Form
                form={form}
                layout="vertical"
                onFinish={handleSubmit}
                initialValues={{
                  guests: 1
                }}
              >
                <Form.Item
                  label="Ngày nhận và trả phòng"
                  required
                >
                  <RangePicker
                    style={{ width: '100%' }}
                    placeholder={['Ngày nhận phòng', 'Ngày trả phòng']}
                    format="DD/MM/YYYY"
                    value={dates}
                    onChange={handleDateChange}
                    disabledDate={(current) => {
                      return current && current < dayjs().startOf('day');
                    }}
                  />
                </Form.Item>

                <Form.Item
                  label="Số khách"
                  required
                >
                  <InputNumber
                    min={1}
                    max={room.max_guests}
                    value={guests}
                    onChange={setGuests}
                    style={{ width: '100%' }}
                    prefix={<UserOutlined />}
                  />
                </Form.Item>

                <Divider />

                <Form.Item
                  name="guest_name"
                  label="Họ và tên"
                  rules={[{ required: true, message: 'Vui lòng nhập họ tên' }]}
                >
                  <Input prefix={<UserOutlined />} placeholder="Nhập họ và tên" />
                </Form.Item>

                <Form.Item
                  name="guest_phone"
                  label="Số điện thoại"
                  rules={[
                    { required: true, message: 'Vui lòng nhập số điện thoại' },
                    { pattern: /^[0-9+\-\s()]+$/, message: 'Số điện thoại không hợp lệ' }
                  ]}
                >
                  <Input prefix={<PhoneOutlined />} placeholder="Nhập số điện thoại" />
                </Form.Item>

                <Form.Item
                  name="guest_email"
                  label="Email (tùy chọn)"
                  rules={[{ type: 'email', message: 'Email không hợp lệ' }]}
                >
                  <Input prefix={<MailOutlined />} placeholder="Nhập email" />
                </Form.Item>

                <Form.Item
                  name="special_requests"
                  label="Yêu cầu đặc biệt (tùy chọn)"
                >
                  <TextArea 
                    rows={3} 
                    placeholder="Nhập yêu cầu đặc biệt nếu có..."
                  />
                </Form.Item>

                <Divider />

                {/* Price Summary */}
                <div style={{ marginBottom: '16px' }}>
                  <Row justify="space-between">
                    <Text>Giá phòng ({nights} đêm):</Text>
                    <Text>{formatPrice(room.price)} x {nights}</Text>
                  </Row>
                  <Row justify="space-between" style={{ marginTop: '8px' }}>
                    <Text strong style={{ fontSize: '18px' }}>Tổng cộng:</Text>
                    <Text strong style={{ fontSize: '18px', color: '#003580' }}>
                      {formatPrice(totalPrice)}
                    </Text>
                  </Row>
                </div>

                <Button
                  type="primary"
                  htmlType="submit"
                  loading={submitting}
                  disabled={!dates[0] || !dates[1] || nights <= 0}
                  style={{
                    width: '100%',
                    height: '48px',
                    fontSize: '16px',
                    fontWeight: '600',
                    backgroundColor: '#0071c2',
                    borderColor: '#0071c2'
                  }}
                >
                  Đặt phòng ngay - {formatPrice(totalPrice)}
                </Button>
              </Form>
            </Card>
          </Col>
        </Row>
      </div>
    </Layout>
  );
};

export default BookingPage;
