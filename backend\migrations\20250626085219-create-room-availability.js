'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('room_availability', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      room_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'rooms',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      date: {
        type: Sequelize.DATEONLY,
        allowNull: false,
        comment: 'Ngày'
      },
      is_available: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: 'Có sẵn hay không'
      },
      price_override: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: true,
        comment: 'Giá ghi đè cho ngày cụ thể'
      },
      min_stay_override: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: '<PERSON><PERSON> ngày thuê tối thiểu ghi đè'
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '<PERSON>hi chú'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // Add unique constraint to prevent duplicate entries
    await queryInterface.addIndex('room_availability', ['room_id', 'date'], {
      unique: true,
      name: 'unique_room_date'
    });

    // Add indexes for better performance
    await queryInterface.addIndex('room_availability', ['room_id']);
    await queryInterface.addIndex('room_availability', ['date']);
    await queryInterface.addIndex('room_availability', ['is_available']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('room_availability');
  }
};
