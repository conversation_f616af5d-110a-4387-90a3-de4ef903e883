const { Booking, Room, User } = require('../models');
const { Op } = require('sequelize');

const bookingController = {
  // Create a new booking
  async createBooking(req, res) {
    try {
      const {
        room_id,
        check_in_date,
        check_out_date,
        guests,
        special_requests,
        guest_name,
        guest_phone,
        guest_email
      } = req.body;

      const user_id = req.user.id;

      // Validate dates
      const checkIn = new Date(check_in_date);
      const checkOut = new Date(check_out_date);
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      if (checkIn < today) {
        return res.status(400).json({
          success: false,
          message: '<PERSON><PERSON><PERSON> nhận phòng không thể là ngày trong quá khứ'
        });
      }

      if (checkOut <= checkIn) {
        return res.status(400).json({
          success: false,
          message: '<PERSON><PERSON><PERSON> trả phòng phải sau ngày nhận phòng'
        });
      }

      // Check if room exists and get room details
      const room = await Room.findByPk(room_id);
      if (!room) {
        return res.status(404).json({
          success: false,
          message: '<PERSON><PERSON><PERSON> không tồn tại'
        });
      }

      // Check if room can accommodate the number of guests
      if (guests > room.max_guests) {
        return res.status(400).json({
          success: false,
          message: `Phòng chỉ có thể chứa tối đa ${room.max_guests} khách`
        });
      }

      // Check for conflicting bookings
      const conflictingBooking = await Booking.findOne({
        where: {
          room_id,
          status: { [Op.in]: ['confirmed', 'pending'] },
          [Op.or]: [
            {
              check_in_date: {
                [Op.between]: [check_in_date, check_out_date]
              }
            },
            {
              check_out_date: {
                [Op.between]: [check_in_date, check_out_date]
              }
            },
            {
              check_in_date: { [Op.lte]: check_in_date },
              check_out_date: { [Op.gte]: check_out_date }
            }
          ]
        }
      });

      if (conflictingBooking) {
        return res.status(400).json({
          success: false,
          message: 'Phòng đã được đặt trong khoảng thời gian này'
        });
      }

      // Calculate total price
      const nights = Math.ceil((checkOut - checkIn) / (1000 * 60 * 60 * 24));
      const total_price = room.price * nights;

      // Create booking
      const booking = await Booking.create({
        room_id,
        user_id,
        check_in_date,
        check_out_date,
        guests,
        total_price,
        special_requests,
        guest_name,
        guest_phone,
        guest_email,
        status: 'pending'
      });

      // Get booking with room details
      const bookingWithDetails = await Booking.findByPk(booking.id, {
        include: [
          {
            model: Room,
            as: 'room',
            attributes: ['id', 'title', 'price', 'room_type']
          },
          {
            model: User,
            as: 'user',
            attributes: ['id', 'name', 'email']
          }
        ]
      });

      res.status(201).json({
        success: true,
        message: 'Đặt phòng thành công',
        data: { booking: bookingWithDetails }
      });

    } catch (error) {
      console.error('Create booking error:', error);
      res.status(500).json({
        success: false,
        message: 'Lỗi server khi đặt phòng',
        error: error.message
      });
    }
  },

  // Get user's bookings
  async getUserBookings(req, res) {
    try {
      const user_id = req.user.id;
      const { page = 1, limit = 10, status } = req.query;

      const whereConditions = { user_id };
      if (status) {
        whereConditions.status = status;
      }

      const offset = (page - 1) * limit;

      const { count, rows: bookings } = await Booking.findAndCountAll({
        where: whereConditions,
        include: [
          {
            model: Room,
            as: 'room',
            attributes: ['id', 'title', 'price', 'room_type'],
            include: [
              {
                model: User,
                as: 'user',
                attributes: ['id', 'name', 'phone']
              }
            ]
          }
        ],
        order: [['created_at', 'DESC']],
        limit: parseInt(limit),
        offset: parseInt(offset)
      });

      res.json({
        success: true,
        data: {
          bookings,
          pagination: {
            total: count,
            page: parseInt(page),
            limit: parseInt(limit),
            totalPages: Math.ceil(count / limit)
          }
        }
      });

    } catch (error) {
      console.error('Get user bookings error:', error);
      res.status(500).json({
        success: false,
        message: 'Lỗi server khi lấy danh sách đặt phòng',
        error: error.message
      });
    }
  },

  // Get booking by ID
  async getBookingById(req, res) {
    try {
      const { id } = req.params;
      const user_id = req.user.id;

      const booking = await Booking.findOne({
        where: { id, user_id },
        include: [
          {
            model: Room,
            as: 'room',
            include: [
              {
                model: User,
                as: 'user',
                attributes: ['id', 'name', 'phone', 'email']
              }
            ]
          }
        ]
      });

      if (!booking) {
        return res.status(404).json({
          success: false,
          message: 'Không tìm thấy đặt phòng'
        });
      }

      res.json({
        success: true,
        data: { booking }
      });

    } catch (error) {
      console.error('Get booking error:', error);
      res.status(500).json({
        success: false,
        message: 'Lỗi server khi lấy thông tin đặt phòng',
        error: error.message
      });
    }
  },

  // Cancel booking
  async cancelBooking(req, res) {
    try {
      const { id } = req.params;
      const { cancelled_reason } = req.body;
      const user_id = req.user.id;

      const booking = await Booking.findOne({
        where: { id, user_id }
      });

      if (!booking) {
        return res.status(404).json({
          success: false,
          message: 'Không tìm thấy đặt phòng'
        });
      }

      if (booking.status === 'cancelled') {
        return res.status(400).json({
          success: false,
          message: 'Đặt phòng đã được hủy trước đó'
        });
      }

      if (booking.status === 'completed') {
        return res.status(400).json({
          success: false,
          message: 'Không thể hủy đặt phòng đã hoàn thành'
        });
      }

      // Update booking status
      await booking.update({
        status: 'cancelled',
        cancelled_at: new Date(),
        cancelled_reason: cancelled_reason || 'Khách hàng hủy'
      });

      res.json({
        success: true,
        message: 'Hủy đặt phòng thành công',
        data: { booking }
      });

    } catch (error) {
      console.error('Cancel booking error:', error);
      res.status(500).json({
        success: false,
        message: 'Lỗi server khi hủy đặt phòng',
        error: error.message
      });
    }
  },

  // Check room availability
  async checkAvailability(req, res) {
    try {
      const { room_id, check_in_date, check_out_date } = req.query;

      if (!room_id || !check_in_date || !check_out_date) {
        return res.status(400).json({
          success: false,
          message: 'Thiếu thông tin room_id, check_in_date hoặc check_out_date'
        });
      }

      // Check for conflicting bookings
      const conflictingBooking = await Booking.findOne({
        where: {
          room_id,
          status: { [Op.in]: ['confirmed', 'pending'] },
          [Op.or]: [
            {
              check_in_date: {
                [Op.between]: [check_in_date, check_out_date]
              }
            },
            {
              check_out_date: {
                [Op.between]: [check_in_date, check_out_date]
              }
            },
            {
              check_in_date: { [Op.lte]: check_in_date },
              check_out_date: { [Op.gte]: check_out_date }
            }
          ]
        }
      });

      const isAvailable = !conflictingBooking;

      res.json({
        success: true,
        data: {
          available: isAvailable,
          message: isAvailable ? 'Phòng có sẵn' : 'Phòng đã được đặt trong khoảng thời gian này'
        }
      });

    } catch (error) {
      console.error('Check availability error:', error);
      res.status(500).json({
        success: false,
        message: 'Lỗi server khi kiểm tra tình trạng phòng',
        error: error.message
      });
    }
  }
};

module.exports = bookingController;
