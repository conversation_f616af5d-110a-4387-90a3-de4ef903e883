-- Add booking fields to rooms table
ALTER TABLE rooms 
ADD COLUMN IF NOT EXISTS max_guests INT NOT NULL DEFAULT 1 COMMENT 'Số khách tối đa';

ALTER TABLE rooms 
ADD COLUMN IF NOT EXISTS min_stay_days INT NOT NULL DEFAULT 1 COMMENT 'Số ngày thuê tối thiểu';

ALTER TABLE rooms 
ADD COLUMN IF NOT EXISTS max_stay_days INT NULL COMMENT 'Số ngày thuê tối đa';

ALTER TABLE rooms 
ADD COLUMN IF NOT EXISTS check_in_time TIME NULL DEFAULT '14:00:00' COMMENT 'Giờ nhận phòng';

ALTER TABLE rooms 
ADD COLUMN IF NOT EXISTS check_out_time TIME NULL DEFAULT '12:00:00' COMMENT 'Giờ trả phòng';

ALTER TABLE rooms 
ADD COLUMN IF NOT EXISTS instant_book BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Cho phép đặt phòng ngay lập tức';

ALTER TABLE rooms 
ADD COLUMN IF NOT EXISTS cancellation_policy ENUM('flexible', 'moderate', 'strict') NOT NULL DEFAULT 'moderate' COMMENT 'Chính sách hủy phòng';

ALTER TABLE rooms 
ADD COLUMN IF NOT EXISTS house_rules TEXT NULL COMMENT 'Nội quy nhà';

-- Create bookings table
CREATE TABLE IF NOT EXISTS bookings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  room_id INT NOT NULL,
  user_id INT NOT NULL,
  check_in_date DATE NOT NULL COMMENT 'Ngày nhận phòng',
  check_out_date DATE NOT NULL COMMENT 'Ngày trả phòng',
  guests INT NOT NULL DEFAULT 1 COMMENT 'Số khách',
  total_price DECIMAL(15,2) NOT NULL COMMENT 'Tổng giá tiền',
  status ENUM('pending', 'confirmed', 'cancelled', 'completed') NOT NULL DEFAULT 'pending' COMMENT 'Trạng thái đặt phòng',
  payment_status ENUM('pending', 'paid', 'refunded') NOT NULL DEFAULT 'pending' COMMENT 'Trạng thái thanh toán',
  payment_method ENUM('cash', 'bank_transfer', 'credit_card', 'e_wallet') NULL COMMENT 'Phương thức thanh toán',
  special_requests TEXT NULL COMMENT 'Yêu cầu đặc biệt',
  guest_name VARCHAR(100) NOT NULL COMMENT 'Tên khách hàng',
  guest_phone VARCHAR(20) NOT NULL COMMENT 'Số điện thoại khách hàng',
  guest_email VARCHAR(100) NULL COMMENT 'Email khách hàng',
  cancelled_at DATETIME NULL COMMENT 'Thời gian hủy',
  cancelled_reason TEXT NULL COMMENT 'Lý do hủy',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE ON UPDATE CASCADE,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE,
  INDEX idx_room_id (room_id),
  INDEX idx_user_id (user_id),
  INDEX idx_dates (check_in_date, check_out_date),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at)
);

-- Create room_availability table
CREATE TABLE IF NOT EXISTS room_availability (
  id INT AUTO_INCREMENT PRIMARY KEY,
  room_id INT NOT NULL,
  date DATE NOT NULL COMMENT 'Ngày',
  is_available BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Có sẵn hay không',
  price_override DECIMAL(15,2) NULL COMMENT 'Giá ghi đè cho ngày cụ thể',
  min_stay_override INT NULL COMMENT 'Số ngày thuê tối thiểu ghi đè',
  notes TEXT NULL COMMENT 'Ghi chú',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE ON UPDATE CASCADE,
  UNIQUE KEY unique_room_date (room_id, date),
  INDEX idx_room_id (room_id),
  INDEX idx_date (date),
  INDEX idx_available (is_available)
);
