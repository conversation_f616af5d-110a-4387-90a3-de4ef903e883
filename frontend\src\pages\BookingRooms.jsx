import React, { useState, useEffect } from "react";
import {
  Row,
  Col,
  Card,
  Typography,
  Spin,
  Empty,
  Pagination,
  message,
  Tag,
  Image,
  Button,
  Space,
  Input,
  Select,
  Divider,
  Rate,
  Badge,
} from "antd";
import {
  EnvironmentOutlined,
  UserOutlined,
  EyeOutlined,
  HeartOutlined,
  ShareAltOutlined,
  SearchOutlined,
  FilterOutlined,
  WifiOutlined,
  CarOutlined,
  HomeOutlined,
} from "@ant-design/icons";
import { useNavigate, useSearchParams } from "react-router-dom";
import Layout from "../components/layout/Layout";
import { roomAPI, addressAPI } from "../services/api";
import { formatPrice, formatDateTime } from "../utils/formatters";

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

const BookingRooms = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();

  const [loading, setLoading] = useState(false);
  const [rooms, setRooms] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  // Search filters
  const [searchForm, setSearchForm] = useState({
    search: searchParams.get("search") || "",
    province_id: searchParams.get("province_id") || undefined,
    district_id: searchParams.get("district_id") || undefined,
    ward_id: searchParams.get("ward_id") || undefined,
    room_type: searchParams.get("room_type") || undefined,
    min_price: searchParams.get("min_price") || undefined,
    max_price: searchParams.get("max_price") || undefined,
  });

  // Applied filters (only updated when user clicks search)
  const [appliedFilters, setAppliedFilters] = useState({
    search: searchParams.get("search") || "",
    province_id: searchParams.get("province_id") || undefined,
    district_id: searchParams.get("district_id") || undefined,
    ward_id: searchParams.get("ward_id") || undefined,
    room_type: searchParams.get("room_type") || undefined,
    min_price: searchParams.get("min_price") || undefined,
    max_price: searchParams.get("max_price") || undefined,
  });

  // Address data
  const [provinces, setProvinces] = useState([]);
  const [districts, setDistricts] = useState([]);
  const [wards, setWards] = useState([]);
  const [loadingAddress, setLoadingAddress] = useState(false);

  const roomTypes = [
    "Phòng trọ",
    "Chung cư",
    "Nhà riêng",
    "Khách sạn",
    "Homestay",
  ];

  useEffect(() => {
    fetchProvinces();
    fetchRooms();
  }, []);

  useEffect(() => {
    // Update search form when URL params change
    const urlFilters = {};
    for (const [key, value] of searchParams.entries()) {
      if (value) {
        urlFilters[key] = value;
      }
    }
    setSearchForm((prev) => ({ ...prev, ...urlFilters }));
  }, [searchParams]);

  useEffect(() => {
    fetchRooms();
  }, [pagination.current, appliedFilters]);

  const fetchProvinces = async () => {
    try {
      setLoadingAddress(true);
      const response = await addressAPI.getProvinces();
      if (response.success) {
        setProvinces(response.data.provinces);
      }
    } catch (error) {
      console.error("Error fetching provinces:", error);
    } finally {
      setLoadingAddress(false);
    }
  };

  const fetchDistricts = async (provinceId) => {
    try {
      setLoadingAddress(true);
      const response = await addressAPI.getDistricts(provinceId);
      if (response.success) {
        setDistricts(response.data.districts);
      }
    } catch (error) {
      console.error("Error fetching districts:", error);
    } finally {
      setLoadingAddress(false);
    }
  };

  const fetchWards = async (districtId) => {
    try {
      setLoadingAddress(true);
      const response = await addressAPI.getWards(districtId);
      if (response.success) {
        setWards(response.data.wards);
      }
    } catch (error) {
      console.error("Error fetching wards:", error);
    } finally {
      setLoadingAddress(false);
    }
  };

  const fetchRooms = async () => {
    try {
      setLoading(true);

      const params = {
        page: pagination.current,
        limit: pagination.pageSize,
        ...Object.fromEntries(
          Object.entries(appliedFilters).filter(
            ([_, value]) => value !== undefined && value !== ""
          )
        ),
      };

      console.log("🔍 Fetching rooms with params:", params);

      const response = await roomAPI.getAllRooms(params);

      if (response.success) {
        setRooms(response.data.rooms);
        setPagination((prev) => ({
          ...prev,
          total: response.data.pagination.total_items,
        }));
      }
    } catch (error) {
      console.error("Error fetching rooms:", error);
      message.error("Có lỗi xảy ra khi tải danh sách phòng");
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    // Update applied filters
    setAppliedFilters({ ...searchForm });

    // Update URL params
    const params = new URLSearchParams();
    Object.entries(searchForm).forEach(([key, value]) => {
      if (value !== undefined && value !== "") {
        params.append(key, value.toString());
      }
    });
    setSearchParams(params);

    // Reset to first page
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  const handleInputChange = (field, value) => {
    setSearchForm((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Handle cascading dropdowns
    if (field === "province_id") {
      setSearchForm((prev) => ({
        ...prev,
        district_id: undefined,
        ward_id: undefined,
      }));
      setDistricts([]);
      setWards([]);
      if (value) {
        fetchDistricts(value);
      }
    } else if (field === "district_id") {
      setSearchForm((prev) => ({
        ...prev,
        ward_id: undefined,
      }));
      setWards([]);
      if (value) {
        fetchWards(value);
      }
    }
  };

  const handlePageChange = (page) => {
    setPagination((prev) => ({ ...prev, current: page }));
  };

  const handleRoomClick = (roomId) => {
    navigate(`/rooms/${roomId}`);
  };

  const renderSearchBar = () => (
    <Card style={{ marginBottom: 16 }}>
      <Row gutter={[16, 16]} align="middle">
        <Col xs={24} sm={12} md={6}>
          <Input
            placeholder="Tìm kiếm theo tên phòng, địa chỉ..."
            prefix={<SearchOutlined />}
            value={searchForm.search}
            onChange={(e) => handleInputChange("search", e.target.value)}
            onPressEnter={handleSearch}
          />
        </Col>
        <Col xs={24} sm={12} md={4}>
          <Select
            placeholder="Tỉnh/Thành phố"
            value={searchForm.province_id}
            onChange={(value) => handleInputChange("province_id", value)}
            allowClear
            style={{ width: "100%" }}
            loading={loadingAddress}
          >
            {provinces.map((province) => (
              <Option key={province.id} value={province.id}>
                {province.name}
              </Option>
            ))}
          </Select>
        </Col>
        <Col xs={24} sm={12} md={4}>
          <Select
            placeholder="Quận/Huyện"
            value={searchForm.district_id}
            onChange={(value) => handleInputChange("district_id", value)}
            allowClear
            style={{ width: "100%" }}
            disabled={!searchForm.province_id}
            loading={loadingAddress}
          >
            {districts.map((district) => (
              <Option key={district.id} value={district.id}>
                {district.name}
              </Option>
            ))}
          </Select>
        </Col>
        <Col xs={24} sm={12} md={4}>
          <Select
            placeholder="Phường/Xã"
            value={searchForm.ward_id}
            onChange={(value) => handleInputChange("ward_id", value)}
            allowClear
            style={{ width: "100%" }}
            disabled={!searchForm.district_id}
            loading={loadingAddress}
          >
            {wards.map((ward) => (
              <Option key={ward.id} value={ward.id}>
                {ward.name}
              </Option>
            ))}
          </Select>
        </Col>
        <Col xs={24} sm={12} md={4}>
          <Button
            type="primary"
            icon={<SearchOutlined />}
            onClick={handleSearch}
            loading={loading}
            style={{ width: "100%" }}
          >
            Tìm kiếm
          </Button>
        </Col>
      </Row>
    </Card>
  );

  return (
    <Layout>
      <div
        style={{
          padding: "24px",
          backgroundColor: "#f5f5f5",
          minHeight: "100vh",
        }}
      >
        <div style={{ maxWidth: 1200, margin: "0 auto" }}>
          <Title level={2} style={{ marginBottom: 24 }}>
            Tìm kiếm phòng trọ
          </Title>

          {/* Search Bar */}
          {renderSearchBar()}

          <Row gutter={24}>
            {/* Left Sidebar - Filters */}
            <Col xs={24} lg={6}>
              <Card
                title={
                  <>
                    <FilterOutlined /> Bộ lọc
                  </>
                }
                style={{ marginBottom: 16 }}
              >
                <Space
                  direction="vertical"
                  style={{ width: "100%" }}
                  size="middle"
                >
                  <div>
                    <Text strong>Loại phòng</Text>
                    <Select
                      placeholder="Chọn loại phòng"
                      value={searchForm.room_type}
                      onChange={(value) =>
                        handleInputChange("room_type", value)
                      }
                      allowClear
                      style={{ width: "100%", marginTop: 8 }}
                    >
                      {roomTypes.map((type) => (
                        <Option key={type} value={type}>
                          {type}
                        </Option>
                      ))}
                    </Select>
                  </div>

                  <div>
                    <Text strong>Khoảng giá</Text>
                    <Row gutter={8} style={{ marginTop: 8 }}>
                      <Col span={12}>
                        <Input
                          placeholder="Từ"
                          type="number"
                          value={searchForm.min_price}
                          onChange={(e) =>
                            handleInputChange("min_price", e.target.value)
                          }
                          suffix="đ"
                        />
                      </Col>
                      <Col span={12}>
                        <Input
                          placeholder="Đến"
                          type="number"
                          value={searchForm.max_price}
                          onChange={(e) =>
                            handleInputChange("max_price", e.target.value)
                          }
                          suffix="đ"
                        />
                      </Col>
                    </Row>
                  </div>

                  <Button
                    type="primary"
                    onClick={handleSearch}
                    loading={loading}
                    style={{ width: "100%" }}
                  >
                    Áp dụng bộ lọc
                  </Button>
                </Space>
              </Card>
            </Col>

            {/* Right Content - Room List */}
            <Col xs={24} lg={18}>
              <div style={{ marginBottom: 16 }}>
                <Text type="secondary">Tìm thấy {pagination.total} phòng</Text>
              </div>

              {loading ? (
                <div style={{ textAlign: "center", padding: "50px 0" }}>
                  <Spin size="large" />
                </div>
              ) : rooms.length === 0 ? (
                <Empty description="Không tìm thấy phòng nào" />
              ) : (
                <Space
                  direction="vertical"
                  style={{ width: "100%" }}
                  size="large"
                >
                  {rooms.map((room) => (
                    <Card
                      key={room.id}
                      hoverable
                      style={{
                        cursor: "pointer",
                        border: "1px solid #e8e8e8",
                        borderRadius: 8,
                        overflow: "hidden",
                      }}
                      bodyStyle={{ padding: 0 }}
                    >
                      <Row gutter={0}>
                        <Col xs={24} sm={10} md={10} lg={10}>
                          <div
                            style={{
                              position: "relative",
                              height: "280px", // Tăng chiều cao ảnh
                              overflow: "hidden",
                            }}
                          >
                            <Image
                              src={
                                room.images?.[0]?.image_url ||
                                "/placeholder-room.jpg"
                              }
                              alt={room.title}
                              style={{
                                width: "100%",
                                height: "100%",
                                objectFit: "cover",
                              }}
                              preview={false}
                            />
                            <div
                              style={{
                                position: "absolute",
                                top: 8,
                                right: 8,
                                backgroundColor: "rgba(0,0,0,0.6)",
                                borderRadius: 4,
                                padding: "4px 8px",
                              }}
                            >
                              <Text style={{ color: "white", fontSize: 12 }}>
                                {room.images?.length || 1} ảnh
                              </Text>
                            </div>
                          </div>
                        </Col>
                        <Col xs={24} sm={14} md={14} lg={14}>
                          <div
                            style={{
                              padding: 16,
                              display: "flex",
                              flexDirection: "column",
                            }}
                          >
                            <div style={{ flex: 1 }}>
                              <div
                                style={{
                                  display: "flex",
                                  justifyContent: "space-between",
                                  alignItems: "flex-start",
                                  marginBottom: 8,
                                }}
                              >
                                <div style={{ flex: 1 }}>
                                  <Title
                                    level={4}
                                    style={{
                                      marginBottom: 4,
                                      color: "#003580",
                                    }}
                                  >
                                    {room.title}
                                  </Title>
                                  <Text
                                    type="secondary"
                                    style={{ fontSize: 13 }}
                                  >
                                    <EnvironmentOutlined />{" "}
                                    {room.address?.street_detail},{" "}
                                    {room.address?.ward?.name},{" "}
                                    {room.address?.ward?.district?.name}
                                  </Text>
                                </div>
                                <Button
                                  icon={<HeartOutlined />}
                                  type="text"
                                  style={{ color: "#666" }}
                                />
                              </div>

                              <div style={{ marginBottom: 8 }}>
                                <Space size="small">
                                  <Tag color="blue">{room.room_type}</Tag>
                                  <Text
                                    type="secondary"
                                    style={{ fontSize: 13 }}
                                  >
                                    <UserOutlined /> {room.max_guests} khách
                                  </Text>
                                </Space>
                              </div>

                              <Paragraph
                                ellipsis={{ rows: 2 }}
                                style={{
                                  marginBottom: 8,
                                  fontSize: 13,
                                  color: "#666",
                                }}
                              >
                                {room.description}
                              </Paragraph>

                              <div
                                style={{
                                  display: "flex",
                                  gap: 8,
                                  marginBottom: 8,
                                }}
                              >
                                <Tag icon={<WifiOutlined />} color="green">
                                  WiFi miễn phí
                                </Tag>
                                <Tag icon={<CarOutlined />} color="orange">
                                  Chỗ đậu xe
                                </Tag>
                              </div>
                            </div>

                            <div
                              style={{
                                display: "flex",
                                justifyContent: "space-between",
                                alignItems: "center",
                                borderTop: "1px solid #f0f0f0",
                                paddingTop: 12,
                              }}
                            >
                              <div>
                                <div style={{ textAlign: "right" }}>
                                  <Text
                                    type="secondary"
                                    style={{ fontSize: 12 }}
                                  >
                                    1 đêm, 1 khách
                                  </Text>
                                  <div>
                                    <Text
                                      strong
                                      style={{ fontSize: 18, color: "#003580" }}
                                    >
                                      {formatPrice(room.price)}
                                    </Text>
                                  </div>
                                  <Text
                                    type="secondary"
                                    style={{ fontSize: 11 }}
                                  >
                                    Đã bao gồm thuế và phí
                                  </Text>
                                </div>
                              </div>
                              <div>
                                <Space>
                                  <Button
                                    type="primary"
                                    size="large"
                                    onClick={() => handleRoomClick(room.id)}
                                    style={{
                                      backgroundColor: "#0071c2",
                                      borderColor: "#0071c2",
                                      fontWeight: 600,
                                    }}
                                  >
                                    Xem phòng trống
                                  </Button>
                                </Space>
                              </div>
                            </div>
                          </div>
                        </Col>
                      </Row>
                    </Card>
                  ))}
                </Space>
              )}

              {/* Pagination */}
              {pagination.total > pagination.pageSize && (
                <div style={{ textAlign: "center", marginTop: 32 }}>
                  <Pagination
                    current={pagination.current}
                    total={pagination.total}
                    pageSize={pagination.pageSize}
                    onChange={handlePageChange}
                    showSizeChanger={false}
                    showQuickJumper
                    showTotal={(total, range) =>
                      `${range[0]}-${range[1]} của ${total} phòng`
                    }
                  />
                </div>
              )}
            </Col>
          </Row>
        </div>
      </div>
    </Layout>
  );
};

export default BookingRooms;
