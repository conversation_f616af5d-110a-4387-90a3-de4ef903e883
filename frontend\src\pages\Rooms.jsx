import React, { useState, useEffect } from "react";
import {
  Row,
  Col,
  Card,
  Typography,
  Spin,
  Empty,
  Pagination,
  message,
  Tag,
  Image,
  Button,
  Space,
} from "antd";
import {
  EnvironmentOutlined,
  UserOutlined,
  EyeOutlined,
  HeartOutlined,
  ShareAltOutlined,
} from "@ant-design/icons";
import { useNavigate, useSearchParams } from "react-router-dom";
import Layout from "../components/layout/Layout";
import SearchFilter from "../components/room/SearchFilter";
import { roomAPI } from "../services/api";
import { formatPrice, formatDateTime } from "../utils/formatters";

const { Title, Text, Paragraph } = Typography;
const { Meta } = Card;

const Rooms = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();

  const [loading, setLoading] = useState(false);
  const [rooms, setRooms] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 12,
    total: 0,
  });

  // Get initial filters from URL params
  const getFiltersFromParams = () => {
    const filters = {};
    for (const [key, value] of searchParams.entries()) {
      if (value) {
        filters[key] = value;
      }
    }
    return filters;
  };

  const [filters, setFilters] = useState(getFiltersFromParams());

  // Update filters when URL params change
  useEffect(() => {
    const urlFilters = getFiltersFromParams();
    setFilters(urlFilters);
  }, [searchParams]);

  useEffect(() => {
    fetchRooms();
  }, [filters, pagination.current]);

  const fetchRooms = async () => {
    try {
      setLoading(true);

      const params = {
        page: pagination.current,
        limit: pagination.pageSize,
        ...filters,
      };

      console.log("🔍 Fetching rooms with params:", params);

      const response = await roomAPI.getAllRooms(params);

      if (response.success) {
        setRooms(response.data.rooms);
        setPagination((prev) => ({
          ...prev,
          total: response.data.pagination.total_items,
        }));
      }
    } catch (error) {
      console.error("Error fetching rooms:", error);
      message.error("Không thể tải danh sách phòng");
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (newFilters) => {
    console.log("🔍 Search filters:", newFilters);

    setFilters(newFilters);
    setPagination((prev) => ({ ...prev, current: 1 }));

    // Update URL params
    const params = new URLSearchParams();
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== "") {
        params.set(key, value);
      }
    });
    setSearchParams(params);
  };

  const handlePageChange = (page, pageSize) => {
    setPagination((prev) => ({
      ...prev,
      current: page,
      pageSize: pageSize,
    }));
  };

  const getAddressString = (room) => {
    const address = room.address;
    if (!address) return "Chưa có địa chỉ";

    const parts = [];
    if (address.street_detail) parts.push(address.street_detail);
    if (address.ward?.name) parts.push(address.ward.name);
    if (address.ward?.district?.name) parts.push(address.ward.district.name);
    if (address.ward?.district?.province?.name)
      parts.push(address.ward.district.province.name);

    return parts.join(", ");
  };

  const getPrimaryImage = (room) => {
    if (room.images && room.images.length > 0) {
      const primaryImage =
        room.images.find((img) => img.is_primary) || room.images[0];
      return primaryImage.image_url;
    }
    return null;
  };

  return (
    <Layout>
      <div style={{ padding: "24px" }}>
        <Title level={2} style={{ textAlign: "center", marginBottom: "32px" }}>
          Tìm kiếm phòng trọ
        </Title>

        {/* Search Filter */}
        <SearchFilter
          onSearch={handleSearch}
          loading={loading}
          initialValues={filters}
        />

        {/* Results */}
        <div style={{ marginBottom: "24px" }}>
          <Text type="secondary">
            Tìm thấy {pagination.total} phòng
            {Object.keys(filters).length > 0 && (
              <span> với bộ lọc đã chọn</span>
            )}
          </Text>
        </div>

        {loading ? (
          <div style={{ textAlign: "center", padding: "50px 0" }}>
            <Spin size="large" />
          </div>
        ) : rooms.length === 0 ? (
          <Empty
            description="Không tìm thấy phòng nào"
            style={{ padding: "50px 0" }}
          />
        ) : (
          <>
            <Row gutter={[16, 16]}>
              {rooms.map((room) => (
                <Col xs={24} sm={12} md={8} lg={6} key={room.id}>
                  <Card
                    hoverable
                    cover={
                      <div style={{ height: "200px", overflow: "hidden" }}>
                        <Image
                          alt={room.title}
                          src={getPrimaryImage(room)}
                          style={{
                            width: "100%",
                            height: "100%",
                            objectFit: "cover",
                          }}
                          fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
                        />
                      </div>
                    }
                    actions={[
                      <Button
                        type="text"
                        icon={<EyeOutlined />}
                        onClick={() => navigate(`/rooms/${room.id}`)}
                      >
                        Xem chi tiết
                      </Button>,
                    ]}
                  >
                    <Meta
                      title={
                        <div>
                          <Text strong style={{ fontSize: "16px" }}>
                            {room.title}
                          </Text>
                          <div style={{ marginTop: "8px" }}>
                            <Tag
                              color="red"
                              style={{ fontSize: "14px", fontWeight: "bold" }}
                            >
                              {formatPrice(room.price)}/tháng
                            </Tag>
                          </div>
                        </div>
                      }
                      description={
                        <div>
                          <Paragraph
                            ellipsis={{ rows: 2 }}
                            style={{ marginBottom: "8px", color: "#666" }}
                          >
                            {room.description}
                          </Paragraph>

                          <div style={{ marginBottom: "8px" }}>
                            <EnvironmentOutlined
                              style={{ color: "#1890ff", marginRight: "4px" }}
                            />
                            <Text type="secondary" style={{ fontSize: "12px" }}>
                              {getAddressString(room)}
                            </Text>
                          </div>

                          <Space size="small">
                            <Tag>{room.room_type}</Tag>
                            {room.area && <Tag>{room.area}m²</Tag>}
                          </Space>

                          <div
                            style={{
                              marginTop: "8px",
                              display: "flex",
                              justifyContent: "space-between",
                              alignItems: "center",
                            }}
                          >
                            <div>
                              <UserOutlined
                                style={{ marginRight: "4px", color: "#666" }}
                              />
                              <Text
                                type="secondary"
                                style={{ fontSize: "12px" }}
                              >
                                {room.user?.name}
                              </Text>
                            </div>
                            <Text type="secondary" style={{ fontSize: "12px" }}>
                              {formatDateTime(room.created_at)}
                            </Text>
                          </div>
                        </div>
                      }
                    />
                  </Card>
                </Col>
              ))}
            </Row>

            {/* Pagination */}
            <div style={{ textAlign: "center", marginTop: "32px" }}>
              <Pagination
                current={pagination.current}
                pageSize={pagination.pageSize}
                total={pagination.total}
                onChange={handlePageChange}
                showSizeChanger
                showQuickJumper
                showTotal={(total, range) =>
                  `${range[0]}-${range[1]} của ${total} phòng`
                }
                pageSizeOptions={["12", "24", "48"]}
              />
            </div>
          </>
        )}
      </div>
    </Layout>
  );
};

export default Rooms;
