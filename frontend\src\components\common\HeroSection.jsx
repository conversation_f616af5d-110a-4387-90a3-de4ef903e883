import React from 'react';
import { Typography, Button, Space } from 'antd';
import { SearchOutlined, PlusOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

const HeroSection = ({ onSearchClick, onCreateRoomClick }) => {
  return (
    <div style={{
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '80px 24px',
      textAlign: 'center',
      color: '#fff'
    }}>
      <div style={{
        maxWidth: '800px',
        margin: '0 auto'
      }}>
        <Title 
          level={1} 
          style={{ 
            color: '#fff', 
            fontSize: '48px', 
            fontWeight: '700',
            marginBottom: '16px',
            lineHeight: '1.2'
          }}
        >
          Tìm chỗ ở hoàn hảo cho chuyến đi của bạn
        </Title>
        
        <Text style={{ 
          fontSize: '20px', 
          color: 'rgba(255,255,255,0.9)',
          display: 'block',
          marginBottom: '40px',
          lineHeight: '1.5'
        }}>
          <PERSON>hám phá hàng ngàn chỗ ở độc đáo từ phòng trọ đến villa sang trọng
        </Text>

        <Space size="large">
          <Button
            type="primary"
            size="large"
            icon={<SearchOutlined />}
            onClick={onSearchClick}
            style={{
              backgroundColor: '#FF385C',
              borderColor: '#FF385C',
              height: '56px',
              padding: '0 32px',
              fontSize: '16px',
              fontWeight: '600',
              borderRadius: '28px'
            }}
          >
            Bắt đầu tìm kiếm
          </Button>
          
          <Button
            size="large"
            icon={<PlusOutlined />}
            onClick={onCreateRoomClick}
            style={{
              backgroundColor: 'rgba(255,255,255,0.2)',
              borderColor: 'rgba(255,255,255,0.3)',
              color: '#fff',
              height: '56px',
              padding: '0 32px',
              fontSize: '16px',
              fontWeight: '600',
              borderRadius: '28px'
            }}
          >
            Cho thuê chỗ ở
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default HeroSection;
