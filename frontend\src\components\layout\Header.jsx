import React, { useState, useEffect } from "react";
import {
  Layout,
  Menu,
  Button,
  Space,
  Dropdown,
  Avatar,
  message,
  Input,
} from "antd";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import {
  HomeOutlined,
  PlusOutlined,
  UserOutlined,
  LoginOutlined,
  LogoutOutlined,
  EnvironmentOutlined,
  SettingOutlined,
  StarOutlined,
  SearchOutlined,
  GlobalOutlined,
  MenuOutlined,
} from "@ant-design/icons";

const { Header: AntHeader } = Layout;

const Header = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout, isAdmin } = useAuth();

  const handleLogout = () => {
    logout();
    message.success("Đăng xuất thành công!");
    navigate("/");
  };

  return (
    <AntHeader
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        zIndex: 1000,
        backgroundColor: "#fff",
        borderBottom: "1px solid #e8e8e8",
        padding: "0 24px",
        height: "80px",
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        boxShadow: "0 1px 2px rgba(0,0,0,0.08)",
      }}
    >
      {/* Logo */}
      <div
        style={{
          display: "flex",
          alignItems: "center",
          cursor: "pointer",
          fontSize: "24px",
          fontWeight: "bold",
          color: "#FF385C",
        }}
        onClick={() => navigate("/")}
      >
        <span style={{ marginRight: "8px" }}>🏠</span>
        <span>dulichviet</span>
      </div>

      {/* Center Navigation */}
      <div
        style={{
          display: "flex",
          alignItems: "center",
          gap: "32px",
          flex: 1,
          justifyContent: "center",
        }}
      >
        <Link
          to="/"
          style={{
            color: location.pathname === "/" ? "#FF385C" : "#222",
            textDecoration: "none",
            fontWeight: location.pathname === "/" ? "600" : "400",
            fontSize: "16px",
          }}
        >
          Chỗ ở
        </Link>
        <Link
          to="/places"
          style={{
            color: location.pathname === "/places" ? "#FF385C" : "#222",
            textDecoration: "none",
            fontWeight: location.pathname === "/places" ? "600" : "400",
            fontSize: "16px",
          }}
        >
          Trải nghiệm
        </Link>
        <Link
          to="/reviews"
          style={{
            color: location.pathname === "/reviews" ? "#FF385C" : "#222",
            textDecoration: "none",
            fontWeight: location.pathname === "/reviews" ? "600" : "400",
            fontSize: "16px",
          }}
        >
          Trải nghiệm trực tuyến
        </Link>
      </div>

      {/* Right Side */}
      <div style={{ display: "flex", alignItems: "center", gap: "16px" }}>
        {user && (
          <Button
            type="text"
            onClick={() => navigate("/create-room")}
            style={{
              color: "#222",
              fontWeight: "600",
              fontSize: "14px",
              padding: "8px 16px",
              borderRadius: "22px",
            }}
          >
            Cho thuê chỗ ở qua dulichviet
          </Button>
        )}

        <Button
          type="text"
          icon={<GlobalOutlined />}
          style={{
            color: "#222",
            borderRadius: "50%",
            width: "42px",
            height: "42px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        />

        <Dropdown
          menu={{
            items: user
              ? [
                  {
                    key: "my-rooms",
                    label: "Phòng của tôi",
                    onClick: () => navigate("/my-rooms"),
                  },
                  {
                    key: "profile",
                    label: "Tài khoản",
                    onClick: () => navigate("/profile"),
                  },
                  {
                    type: "divider",
                  },
                  {
                    key: "logout",
                    label: "Đăng xuất",
                    onClick: handleLogout,
                  },
                ]
              : [
                  {
                    key: "register",
                    label: "Đăng ký",
                    onClick: () => navigate("/register"),
                  },
                  {
                    key: "login",
                    label: "Đăng nhập",
                    onClick: () => navigate("/login"),
                  },
                ],
          }}
          placement="bottomRight"
          trigger={["click"]}
        >
          <Button
            style={{
              border: "1px solid #ddd",
              borderRadius: "21px",
              height: "42px",
              padding: "5px 5px 5px 12px",
              display: "flex",
              alignItems: "center",
              gap: "12px",
              backgroundColor: "#fff",
            }}
          >
            <MenuOutlined style={{ fontSize: "14px", color: "#717171" }} />
            <Avatar
              size={30}
              icon={<UserOutlined />}
              style={{ backgroundColor: "#717171" }}
            />
          </Button>
        </Dropdown>
      </div>
    </AntHeader>
  );
};

export default Header;
