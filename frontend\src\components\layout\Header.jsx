import React, { useState, useEffect } from "react";
import {
  Layout,
  Menu,
  Button,
  Space,
  Dropdown,
  Avatar,
  message,
  Input,
} from "antd";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import {
  HomeOutlined,
  PlusOutlined,
  UserOutlined,
  LoginOutlined,
  LogoutOutlined,
  EnvironmentOutlined,
  SettingOutlined,
  StarOutlined,
  SearchOutlined,
  GlobalOutlined,
  MenuOutlined,
} from "@ant-design/icons";

const { Header: AntHeader } = Layout;

const Header = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout, isAdmin } = useAuth();

  const handleLogout = () => {
    logout();
    message.success("Đăng xuất thành công!");
    navigate("/");
  };

  return (
    <AntHeader
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        zIndex: 1000,
        backgroundColor: "#003580",
        padding: "0 24px",
        height: "70px",
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
      }}
    >
      {/* Logo */}
      <div
        style={{
          display: "flex",
          alignItems: "center",
          cursor: "pointer",
          fontSize: "24px",
          fontWeight: "bold",
          color: "#fff",
        }}
        onClick={() => navigate("/")}
      >
        <span style={{ marginRight: "8px" }}>🏠</span>
        <span>DuLichViet.com</span>
      </div>

      {/* Center Navigation */}
      <div
        style={{
          display: "flex",
          alignItems: "center",
          gap: "24px",
          flex: 1,
          justifyContent: "center",
        }}
      >
        <Button
          type="text"
          icon={<HomeOutlined />}
          onClick={() => navigate("/")}
          style={{
            color: location.pathname === "/" ? "#febb02" : "#fff",
            fontWeight: "500",
            fontSize: "14px",
            height: "40px",
            padding: "0 16px",
            border:
              location.pathname === "/"
                ? "1px solid #febb02"
                : "1px solid transparent",
            borderRadius: "3px",
          }}
        >
          Chỗ ở
        </Button>

        <Button
          type="text"
          icon={<EnvironmentOutlined />}
          onClick={() => navigate("/places")}
          style={{
            color: location.pathname === "/places" ? "#febb02" : "#fff",
            fontWeight: "500",
            fontSize: "14px",
            height: "40px",
            padding: "0 16px",
            border:
              location.pathname === "/places"
                ? "1px solid #febb02"
                : "1px solid transparent",
            borderRadius: "3px",
          }}
        >
          Địa điểm
        </Button>

        <Button
          type="text"
          icon={<StarOutlined />}
          onClick={() => navigate("/reviews")}
          style={{
            color: location.pathname === "/reviews" ? "#febb02" : "#fff",
            fontWeight: "500",
            fontSize: "14px",
            height: "40px",
            padding: "0 16px",
            border:
              location.pathname === "/reviews"
                ? "1px solid #febb02"
                : "1px solid transparent",
            borderRadius: "3px",
          }}
        >
          Đánh giá
        </Button>

        {user && (
          <Button
            type="text"
            icon={<PlusOutlined />}
            onClick={() => navigate("/create-room")}
            style={{
              color: location.pathname === "/create-room" ? "#febb02" : "#fff",
              fontWeight: "500",
              fontSize: "14px",
              height: "40px",
              padding: "0 16px",
              border:
                location.pathname === "/create-room"
                  ? "1px solid #febb02"
                  : "1px solid transparent",
              borderRadius: "3px",
            }}
          >
            Đăng phòng
          </Button>
        )}
      </div>

      {/* Right Side */}
      <div style={{ display: "flex", alignItems: "center", gap: "12px" }}>
        <Button
          type="text"
          style={{
            color: "#fff",
            fontSize: "14px",
            height: "32px",
            padding: "0 12px",
          }}
        >
          VND
        </Button>

        <Button
          type="text"
          icon={<GlobalOutlined />}
          style={{
            color: "#fff",
            borderRadius: "3px",
            width: "32px",
            height: "32px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        />

        {user ? (
          <Dropdown
            menu={{
              items: [
                {
                  key: "my-rooms",
                  label: "Phòng của tôi",
                  onClick: () => navigate("/my-rooms"),
                },
                {
                  key: "profile",
                  label: "Tài khoản",
                  onClick: () => navigate("/profile"),
                },
                {
                  type: "divider",
                },
                {
                  key: "logout",
                  label: "Đăng xuất",
                  onClick: handleLogout,
                },
              ],
            }}
            placement="bottomRight"
            trigger={["click"]}
          >
            <Button
              style={{
                backgroundColor: "#0071c2",
                border: "1px solid #0071c2",
                borderRadius: "3px",
                height: "36px",
                padding: "0 12px",
                color: "#fff",
                fontWeight: "500",
              }}
            >
              {user.name}
            </Button>
          </Dropdown>
        ) : (
          <Space>
            <Button
              onClick={() => navigate("/register")}
              style={{
                backgroundColor: "transparent",
                border: "1px solid #fff",
                borderRadius: "3px",
                height: "36px",
                padding: "0 16px",
                color: "#fff",
                fontWeight: "500",
              }}
            >
              Đăng ký
            </Button>
            <Button
              type="primary"
              onClick={() => navigate("/login")}
              style={{
                backgroundColor: "#0071c2",
                borderColor: "#0071c2",
                borderRadius: "3px",
                height: "36px",
                padding: "0 16px",
                fontWeight: "500",
              }}
            >
              Đăng nhập
            </Button>
          </Space>
        )}
      </div>
    </AntHeader>
  );
};

export default Header;
