import React from "react";
import { Routes, Route } from "react-router-dom";
import Home from "../pages/Home";
import Places from "../pages/Places";
import PlaceDetail from "../pages/PlaceDetail";
import RoomDetail from "../pages/RoomDetail";
import BookingRoomDetail from "../pages/BookingRoomDetail";
import BookingRooms from "../pages/BookingRooms";
import BookingPage from "../pages/BookingPage";
import CreateRoom from "../pages/CreateRoom";
import CreateReview from "../pages/CreateReview";
import Reviews from "../pages/Reviews";
import ReviewDetail from "../pages/ReviewDetail";
import Rooms from "../pages/Rooms";
import MyRooms from "../pages/MyRooms";
import EditRoom from "../pages/EditRoom";
import Login from "../pages/Login";
import Register from "../pages/Register";
import AdminDashboard from "../pages/admin/AdminDashboard";
import UserManagement from "../pages/admin/UserManagement";
import UserDetail from "../pages/admin/UserDetail";
import ReviewManagement from "../pages/admin/ReviewManagement";
import ProtectedRoute from "../components/ProtectedRoute";
import NotFound from "../pages/NotFound";

const AppRoutes = () => {
  return (
    <Routes>
      <Route path="/" element={<Home />} />
      <Route path="/places" element={<Places />} />
      <Route path="/places/:id" element={<PlaceDetail />} />
      <Route path="/rooms/:id" element={<BookingRoomDetail />} />
      <Route path="/rooms-old/:id" element={<RoomDetail />} />
      <Route
        path="/booking/:roomId"
        element={
          <ProtectedRoute>
            <BookingPage />
          </ProtectedRoute>
        }
      />
      <Route
        path="/create-room"
        element={
          <ProtectedRoute>
            <CreateRoom />
          </ProtectedRoute>
        }
      />
      <Route
        path="/create-review"
        element={
          <ProtectedRoute>
            <CreateReview />
          </ProtectedRoute>
        }
      />
      <Route path="/reviews" element={<Reviews />} />
      <Route path="/reviews/:id" element={<ReviewDetail />} />
      <Route path="/rooms" element={<Rooms />} />
      <Route path="/booking-rooms" element={<BookingRooms />} />
      <Route
        path="/my-rooms"
        element={
          <ProtectedRoute>
            <MyRooms />
          </ProtectedRoute>
        }
      />
      <Route
        path="/edit-room/:id"
        element={
          <ProtectedRoute>
            <EditRoom />
          </ProtectedRoute>
        }
      />

      {/* Admin Routes */}
      <Route
        path="/admin"
        element={
          <ProtectedRoute requireAdmin>
            <AdminDashboard />
          </ProtectedRoute>
        }
      />
      <Route
        path="/admin/users"
        element={
          <ProtectedRoute requireAdmin>
            <UserManagement />
          </ProtectedRoute>
        }
      />
      <Route
        path="/admin/users/:id"
        element={
          <ProtectedRoute requireAdmin>
            <UserDetail />
          </ProtectedRoute>
        }
      />
      <Route
        path="/admin/reviews"
        element={
          <ProtectedRoute requireAdmin>
            <ReviewManagement />
          </ProtectedRoute>
        }
      />

      <Route path="/login" element={<Login />} />
      <Route path="/register" element={<Register />} />
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
};

export default AppRoutes;
