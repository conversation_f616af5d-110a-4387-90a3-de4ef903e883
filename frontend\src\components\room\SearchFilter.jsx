import React, { useState, useEffect } from "react";
import {
  Card,
  Row,
  Col,
  Input,
  Select,
  Button,
  Space,
  InputNumber,
  Collapse,
  Typography,
} from "antd";
import {
  SearchOutlined,
  FilterOutlined,
  ClearOutlined,
  EnvironmentOutlined,
} from "@ant-design/icons";
import { addressAPI } from "../../services/api";

const { Option } = Select;
const { Panel } = Collapse;
const { Text } = Typography;

const SearchFilter = ({ onSearch, loading, initialValues = {} }) => {
  const [searchForm, setSearchForm] = useState({
    search: initialValues.search || "",
    province_id: initialValues.province_id || undefined,
    district_id: initialValues.district_id || undefined,
    ward_id: initialValues.ward_id || undefined,
    room_type: initialValues.room_type || undefined,
    min_price: initialValues.min_price || undefined,
    max_price: initialValues.max_price || undefined,
  });

  const [provinces, setProvinces] = useState([]);
  const [districts, setDistricts] = useState([]);
  const [wards, setWards] = useState([]);
  const [loadingAddress, setLoadingAddress] = useState(false);

  useEffect(() => {
    fetchProvinces();
  }, []);

  // Update form when initialValues change
  useEffect(() => {
    setSearchForm({
      search: initialValues.search || "",
      province_id: initialValues.province_id || undefined,
      district_id: initialValues.district_id || undefined,
      ward_id: initialValues.ward_id || undefined,
      room_type: initialValues.room_type || undefined,
      min_price: initialValues.min_price || undefined,
      max_price: initialValues.max_price || undefined,
    });

    // Load districts if province_id is provided
    if (initialValues.province_id) {
      fetchDistricts(initialValues.province_id);
    }

    // Load wards if district_id is provided
    if (initialValues.district_id) {
      fetchWards(initialValues.district_id);
    }
  }, [initialValues]);

  const fetchProvinces = async () => {
    try {
      setLoadingAddress(true);
      const response = await addressAPI.getProvinces();
      if (response.success) {
        setProvinces(response.data.provinces);
      }
    } catch (error) {
      console.error("Error fetching provinces:", error);
    } finally {
      setLoadingAddress(false);
    }
  };

  const fetchDistricts = async (provinceId) => {
    try {
      setLoadingAddress(true);
      const response = await addressAPI.getDistricts(provinceId);
      if (response.success) {
        setDistricts(response.data.districts);
      }
    } catch (error) {
      console.error("Error fetching districts:", error);
    } finally {
      setLoadingAddress(false);
    }
  };

  const fetchWards = async (districtId) => {
    try {
      setLoadingAddress(true);
      const response = await addressAPI.getWards(districtId);
      if (response.success) {
        setWards(response.data.wards);
      }
    } catch (error) {
      console.error("Error fetching wards:", error);
    } finally {
      setLoadingAddress(false);
    }
  };

  const handleProvinceChange = (value) => {
    setSearchForm((prev) => ({
      ...prev,
      province_id: value,
      district_id: undefined,
      ward_id: undefined,
    }));
    setDistricts([]);
    setWards([]);

    if (value) {
      fetchDistricts(value);
    }
  };

  const handleDistrictChange = (value) => {
    setSearchForm((prev) => ({
      ...prev,
      district_id: value,
      ward_id: undefined,
    }));
    setWards([]);

    if (value) {
      fetchWards(value);
    }
  };

  const handleWardChange = (value) => {
    setSearchForm((prev) => ({
      ...prev,
      ward_id: value,
    }));
  };

  const handleInputChange = (field, value) => {
    setSearchForm((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSearch = () => {
    // Remove undefined values
    const cleanFilters = Object.fromEntries(
      Object.entries(searchForm).filter(
        ([_, value]) => value !== undefined && value !== ""
      )
    );
    onSearch(cleanFilters);
  };

  const handleClear = () => {
    setSearchForm({
      search: "",
      province_id: undefined,
      district_id: undefined,
      ward_id: undefined,
      room_type: undefined,
      min_price: undefined,
      max_price: undefined,
    });
    setDistricts([]);
    setWards([]);
    onSearch({});
  };

  const roomTypes = [
    "Phòng trọ",
    "Chung cư",
    "Nhà riêng",
    "Khách sạn",
    "Homestay",
  ];

  return (
    <Card style={{ marginBottom: "24px" }}>
      <Row gutter={[16, 16]}>
        {/* Search Input */}
        <Col xs={24} md={12} lg={8}>
          <Input
            placeholder="Tìm kiếm theo tên phòng, địa chỉ..."
            prefix={<SearchOutlined />}
            value={searchForm.search}
            onChange={(e) => handleInputChange("search", e.target.value)}
            onPressEnter={handleSearch}
            size="large"
          />
        </Col>

        {/* Province Filter */}
        <Col xs={24} md={6} lg={4}>
          <Select
            placeholder="Tỉnh/Thành phố"
            value={searchForm.province_id}
            onChange={handleProvinceChange}
            loading={loadingAddress}
            allowClear
            showSearch
            filterOption={(input, option) =>
              option.children.toLowerCase().includes(input.toLowerCase())
            }
            size="large"
            style={{ width: "100%" }}
          >
            {provinces.map((province) => (
              <Option key={province.id} value={province.id}>
                {province.name}
              </Option>
            ))}
          </Select>
        </Col>

        {/* District Filter */}
        <Col xs={24} md={6} lg={4}>
          <Select
            placeholder="Quận/Huyện"
            value={searchForm.district_id}
            onChange={handleDistrictChange}
            loading={loadingAddress}
            allowClear
            showSearch
            filterOption={(input, option) =>
              option.children.toLowerCase().includes(input.toLowerCase())
            }
            size="large"
            style={{ width: "100%" }}
            disabled={!searchForm.province_id}
          >
            {districts.map((district) => (
              <Option key={district.id} value={district.id}>
                {district.name}
              </Option>
            ))}
          </Select>
        </Col>

        {/* Ward Filter */}
        <Col xs={24} md={6} lg={4}>
          <Select
            placeholder="Phường/Xã"
            value={searchForm.ward_id}
            onChange={handleWardChange}
            loading={loadingAddress}
            allowClear
            showSearch
            filterOption={(input, option) =>
              option.children.toLowerCase().includes(input.toLowerCase())
            }
            size="large"
            style={{ width: "100%" }}
            disabled={!searchForm.district_id}
          >
            {wards.map((ward) => (
              <Option key={ward.id} value={ward.id}>
                {ward.name}
              </Option>
            ))}
          </Select>
        </Col>

        {/* Search Button */}
        <Col xs={24} md={6} lg={4}>
          <Space style={{ width: "100%" }}>
            <Button
              type="primary"
              icon={<SearchOutlined />}
              onClick={handleSearch}
              loading={loading}
              size="large"
            >
              Tìm kiếm
            </Button>
            <Button icon={<ClearOutlined />} onClick={handleClear} size="large">
              Xóa
            </Button>
          </Space>
        </Col>
      </Row>

      {/* Advanced Filters */}
      <Collapse ghost style={{ marginTop: "16px" }}>
        <Panel
          header={
            <Text>
              <FilterOutlined /> Bộ lọc nâng cao
            </Text>
          }
          key="advanced"
        >
          <Row gutter={[16, 16]}>
            {/* Room Type */}
            <Col xs={24} md={8}>
              <Text strong>Loại phòng:</Text>
              <Select
                placeholder="Chọn loại phòng"
                value={searchForm.room_type}
                onChange={(value) => handleInputChange("room_type", value)}
                allowClear
                style={{ width: "100%", marginTop: "8px" }}
              >
                {roomTypes.map((type) => (
                  <Option key={type} value={type}>
                    {type}
                  </Option>
                ))}
              </Select>
            </Col>

            {/* Price Range */}
            <Col xs={24} md={8}>
              <Text strong>Khoảng giá (VNĐ):</Text>
              <Row gutter={8} style={{ marginTop: "8px" }}>
                <Col span={12}>
                  <InputNumber
                    placeholder="Giá từ"
                    value={searchForm.min_price}
                    onChange={(value) => handleInputChange("min_price", value)}
                    formatter={(value) =>
                      `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                    }
                    parser={(value) => value.replace(/\$\s?|(,*)/g, "")}
                    style={{ width: "100%" }}
                    min={0}
                  />
                </Col>
                <Col span={12}>
                  <InputNumber
                    placeholder="Giá đến"
                    value={searchForm.max_price}
                    onChange={(value) => handleInputChange("max_price", value)}
                    formatter={(value) =>
                      `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                    }
                    parser={(value) => value.replace(/\$\s?|(,*)/g, "")}
                    style={{ width: "100%" }}
                    min={0}
                  />
                </Col>
              </Row>
            </Col>
          </Row>
        </Panel>
      </Collapse>
    </Card>
  );
};

export default SearchFilter;
