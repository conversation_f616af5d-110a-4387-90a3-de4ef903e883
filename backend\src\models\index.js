const User = require('./User');
const Province = require('./Province');
const District = require('./District');
const Ward = require('./Ward');
const Address = require('./Address');
const Place = require('./Place');
const Room = require('./Room');
const Review = require('./Review');
const RoomImage = require('./RoomImage');
const Rating = require('./Rating');
const Favorite = require('./Favorite');
const Booking = require('./Booking');
const RoomAvailability = require('./RoomAvailability');

// Define associations
// Geographic hierarchy
Province.hasMany(District, { foreignKey: 'province_id', as: 'districts' });
District.belongsTo(Province, { foreignKey: 'province_id', as: 'province' });
District.hasMany(Ward, { foreignKey: 'district_id', as: 'wards' });
Ward.belongsTo(District, { foreignKey: 'district_id', as: 'district' });
Ward.hasMany(Address, { foreignKey: 'ward_id', as: 'addresses' });
Address.belongsTo(Ward, { foreignKey: 'ward_id', as: 'ward' });

// User associations
User.hasMany(Room, { foreignKey: 'user_id', as: 'rooms' });
User.hasMany(Review, { foreignKey: 'user_id', as: 'reviews' });
User.hasMany(Rating, { foreignKey: 'user_id', as: 'ratings' });
User.hasMany(Favorite, { foreignKey: 'user_id', as: 'favorites' });

// Address associations
Address.hasMany(Place, { foreignKey: 'address_id', as: 'places' });
Address.hasMany(Room, { foreignKey: 'address_id', as: 'rooms' });

// Place associations
Place.belongsTo(Address, { foreignKey: 'address_id', as: 'address' });
Place.hasMany(Review, { foreignKey: 'place_id', as: 'reviews' });

// Room associations
Room.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
Room.belongsTo(Address, { foreignKey: 'address_id', as: 'address' });
Room.hasMany(RoomImage, { foreignKey: 'room_id', as: 'images' });
Room.hasMany(Favorite, { foreignKey: 'room_id', as: 'favorites' });
Room.hasMany(Booking, { foreignKey: 'room_id', as: 'bookings' });
Room.hasMany(RoomAvailability, { foreignKey: 'room_id', as: 'availability' });

// Review associations
Review.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
Review.belongsTo(Place, { foreignKey: 'place_id', as: 'place' });

// RoomImage associations
RoomImage.belongsTo(Room, { foreignKey: 'room_id', as: 'room' });

// Rating associations
Rating.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

// Favorite associations
Favorite.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
Favorite.belongsTo(Room, { foreignKey: 'room_id', as: 'room' });

// Booking associations
Booking.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
Booking.belongsTo(Room, { foreignKey: 'room_id', as: 'room' });
User.hasMany(Booking, { foreignKey: 'user_id', as: 'bookings' });

// RoomAvailability associations
RoomAvailability.belongsTo(Room, { foreignKey: 'room_id', as: 'room' });

module.exports = {
  User,
  Province,
  District,
  Ward,
  Address,
  Place,
  Room,
  Review,
  RoomImage,
  Rating,
  Favorite,
  Booking,
  RoomAvailability
};
