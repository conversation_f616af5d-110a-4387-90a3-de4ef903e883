import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Row,
  Col,
  Typography,
  Card,
  Button,
  Space,
  Tag,
  Avatar,
  Rate,
  Spin,
  Empty,
  Pagination,
  Image,
  Divider,
  Input,
} from "antd";
import {
  UserOutlined,
  EnvironmentOutlined,
  CalendarOutlined,
  EyeOutlined,
  LikeOutlined,
  CommentOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import Layout from "../components/layout/Layout";
import { reviewAPI } from "../services/api";
import { formatDateTime } from "../utils/formatters";

const { Title, Text, Paragraph } = Typography;
const { Meta } = Card;

const Reviews = () => {
  const navigate = useNavigate();
  const [reviews, setReviews] = useState([]);
  const [featuredReview, setFeaturedReview] = useState(null);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(6);
  const [totalReviews, setTotalReviews] = useState(0);
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    fetchReviews();
  }, [currentPage]);

  const fetchReviews = async () => {
    try {
      setLoading(true);
      const params = {
        page: currentPage,
        limit: pageSize,
      };

      const response = await reviewAPI.getAllReviews(params);

      if (response.success) {
        const allReviews = response.data.reviews;

        // Set featured review (first review with image)
        if (currentPage === 1) {
          const featured =
            allReviews.find((review) => review.featured_image) || allReviews[0];
          setFeaturedReview(featured);

          // Remove featured review from regular list
          const remainingReviews = allReviews.filter(
            (review) => review.id !== featured?.id
          );
          console.log("check", allReviews);
          setReviews(allReviews);
        } else {
          setReviews(allReviews);
        }

        setTotalReviews(response.data.pagination.total_items);
      }
    } catch (error) {
      console.error("Error fetching reviews:", error);
      setReviews([]);
      setTotalReviews(0);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const handleViewDetail = (reviewId) => {
    navigate(`/reviews/${reviewId}`);
  };

  const handleCreateReview = () => {
    navigate("/create-review");
  };

  const handleSearch = () => {
    // Implement search functionality
    console.log("Searching for:", searchQuery);
  };

  const stripHtml = (html) => {
    const tmp = document.createElement("div");
    tmp.innerHTML = html;
    return tmp.textContent || tmp.innerText || "";
  };

  if (loading) {
    return (
      <Layout>
        <div style={{ textAlign: "center", padding: "100px 0" }}>
          <Spin size="large" />
        </div>
      </Layout>
    );
  }

  return (
    <div style={{ minHeight: "100vh" }}>
      {/* Hero Section */}
      <div
        style={{
          background:
            'linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)), url("https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80")',
          backgroundSize: "cover",
          backgroundPosition: "center",
          height: "400px",
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          alignItems: "center",
          color: "white",
          textAlign: "center",
          position: "relative",
        }}
      >
        {/* Search Bar */}
        <div
          style={{
            position: "absolute",
            top: "20px",
            left: "50%",
            transform: "translateX(-50%)",
            width: "90%",
            maxWidth: "800px",
          }}
        >
          <Input.Search
            placeholder="Tìm kiếm những bài viết truyền cảm hứng, mẹo, điểm đến..."
            size="large"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onSearch={handleSearch}
            style={{
              borderRadius: "8px",
            }}
            enterButton={
              <Button type="primary" style={{ borderRadius: "0 8px 8px 0" }}>
                <SearchOutlined /> Tìm kiếm
              </Button>
            }
          />
        </div>

        {/* Featured Article Title */}
        {featuredReview && (
          <div
            style={{ marginTop: "100px", maxWidth: "800px", padding: "0 20px" }}
          >
            <Title
              level={1}
              style={{
                color: "white",
                fontSize: "48px",
                fontWeight: "bold",
                textShadow: "2px 2px 4px rgba(0,0,0,0.5)",
                marginBottom: "16px",
              }}
            >
              {featuredReview.title}
            </Title>
            <div style={{ fontSize: "18px", marginBottom: "20px" }}>
              <Text style={{ color: "white" }}>
                {featuredReview.user?.name || "Traveloka VN"} •{" "}
                {formatDateTime(featuredReview.created_at)}
              </Text>
            </div>

            {/* Navigation dots */}
            <div
              style={{ display: "flex", gap: "8px", justifyContent: "center" }}
            >
              <div
                style={{
                  width: "12px",
                  height: "12px",
                  borderRadius: "50%",
                  backgroundColor: "white",
                }}
              />
              <div
                style={{
                  width: "12px",
                  height: "12px",
                  borderRadius: "50%",
                  backgroundColor: "rgba(255,255,255,0.5)",
                }}
              />
              <div
                style={{
                  width: "12px",
                  height: "12px",
                  borderRadius: "50%",
                  backgroundColor: "rgba(255,255,255,0.5)",
                }}
              />
            </div>
          </div>
        )}
      </div>

      <Layout style={{ backgroundColor: "#f5f5f5" }}>
        {/* Latest Articles Section */}
        <div style={{ padding: "40px 24px" }}>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: "24px",
            }}
          >
            <div>
              <Title level={2} style={{ margin: 0, marginBottom: "8px" }}>
                Các bài viết mới nhất
              </Title>
              <Text type="secondary">
                Luôn dẫn đầu và nắm bắt những nguồn cảm hứng mới mẻ nhất
              </Text>
            </div>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              size="large"
              onClick={handleCreateReview}
            >
              Viết bài review
            </Button>
          </div>

          {reviews.length === 0 && !featuredReview ? (
            <Empty
              description="Chưa có bài viết nào"
              style={{ padding: "50px 0" }}
            >
              <Button type="primary" onClick={handleCreateReview}>
                Viết bài review đầu tiên
              </Button>
            </Empty>
          ) : (
            <>
              <Row gutter={[24, 24]}>
                {reviews.map((review) => (
                  <Col xs={24} sm={12} lg={6} key={review.id}>
                    <Card
                      hoverable
                      style={{
                        height: "100%",
                        borderRadius: "12px",
                        overflow: "hidden",
                        border: "none",
                        boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
                      }}
                      bodyStyle={{ padding: "16px" }}
                      cover={
                        <div
                          style={{
                            height: "200px",
                            overflow: "hidden",
                            position: "relative",
                            cursor: "pointer",
                          }}
                          onClick={() => handleViewDetail(review.id)}
                        >
                          {review.featured_image ? (
                            <Image
                              alt={review.title}
                              src={review.featured_image}
                              style={{
                                width: "100%",
                                height: "100%",
                                objectFit: "cover",
                              }}
                              preview={false}
                            />
                          ) : (
                            <div
                              style={{
                                height: "200px",
                                backgroundColor: "#f5f5f5",
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                              }}
                            >
                              <EnvironmentOutlined
                                style={{ fontSize: "48px", color: "#d9d9d9" }}
                              />
                            </div>
                          )}

                          {/* Overlay with location */}
                          <div
                            style={{
                              position: "absolute",
                              bottom: "12px",
                              left: "12px",
                              background: "rgba(0,0,0,0.7)",
                              color: "white",
                              padding: "4px 8px",
                              borderRadius: "4px",
                              fontSize: "12px",
                            }}
                          >
                            <EnvironmentOutlined />{" "}
                            {review.place?.name || "Địa điểm"}
                          </div>
                        </div>
                      }
                    >
                      <div style={{ padding: "0" }}>
                        <Title
                          level={5}
                          style={{
                            margin: 0,
                            marginBottom: "12px",
                            fontSize: "16px",
                            fontWeight: "600",
                            cursor: "pointer",
                          }}
                          onClick={() => handleViewDetail(review.id)}
                        >
                          {review.title}
                        </Title>

                        <Paragraph
                          ellipsis={{ rows: 2 }}
                          style={{
                            marginBottom: "16px",
                            color: "#666",
                            fontSize: "14px",
                          }}
                        >
                          {stripHtml(review.content)}
                        </Paragraph>

                        <div
                          style={{
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                            marginBottom: "12px",
                          }}
                        >
                          <Space>
                            <Avatar size="small" icon={<UserOutlined />} />
                            <Text
                              style={{ fontSize: "12px", fontWeight: "500" }}
                            >
                              {review.user?.name || "Traveloka VN"}
                            </Text>
                          </Space>
                          <Text type="secondary" style={{ fontSize: "12px" }}>
                            {formatDateTime(review.created_at)}
                          </Text>
                        </div>

                        <div
                          style={{
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                          }}
                        >
                          <Space>
                            <Button
                              type="text"
                              icon={<LikeOutlined />}
                              size="small"
                              style={{ padding: "0", fontSize: "12px" }}
                            >
                              {review.likes_count || 0}
                            </Button>
                            <Button
                              type="text"
                              icon={<CommentOutlined />}
                              size="small"
                              style={{ padding: "0", fontSize: "12px" }}
                            >
                              {review.comments_count || 0}
                            </Button>
                          </Space>
                          <Button
                            type="link"
                            size="small"
                            onClick={() => handleViewDetail(review.id)}
                            style={{ padding: "0", fontSize: "12px" }}
                          >
                            Đọc thêm →
                          </Button>
                        </div>
                      </div>
                    </Card>
                  </Col>
                ))}
              </Row>

              {totalReviews > pageSize && (
                <div style={{ textAlign: "center", marginTop: "32px" }}>
                  <Pagination
                    current={currentPage}
                    total={totalReviews}
                    pageSize={pageSize}
                    onChange={handlePageChange}
                    showSizeChanger={false}
                    showQuickJumper
                    showTotal={(total, range) =>
                      `${range[0]}-${range[1]} của ${total} bài viết`
                    }
                  />
                </div>
              )}
            </>
          )}
        </div>
      </Layout>
    </div>
  );
};

export default Reviews;
