import React from "react";
import { Row, Col, Typography, Card } from "antd";
import { useNavigate } from "react-router-dom";
import Layout from "../components/layout/Layout";
import BookingHeroSection from "../components/common/BookingHeroSection";

const { Title, Text } = Typography;

const Home = () => {
  const navigate = useNavigate();

  // Property types data
  const propertyTypes = [
    {
      id: 1,
      title: "Hotels",
      subtitle: "Jun 25-Jun 26, 2 adults",
      available: "212 available",
      image:
        "https://images.unsplash.com/photo-1566073771259-6a8506099945?w=300&h=200&fit=crop",
      type: "Khách sạn",
    },
    {
      id: 2,
      title: "Apartments",
      subtitle: "Jun 25-Jun 26, 2 adults",
      available: "130 available",
      image:
        "https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=300&h=200&fit=crop",
      type: "<PERSON><PERSON>n hộ",
    },
    {
      id: 3,
      title: "Resorts",
      subtitle: "Jun 25-Jun 26, 2 adults",
      available: "11 available",
      image:
        "https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=300&h=200&fit=crop",
      type: "Resort",
    },
    {
      id: 4,
      title: "Villas",
      subtitle: "Jun 25-Jun 26, 2 adults",
      available: "19 available",
      image:
        "https://images.unsplash.com/photo-1613490493576-7fde63acd811?w=300&h=200&fit=crop",
      type: "Villa",
    },
  ];

  // Trending destinations data
  const trendingDestinations = [
    {
      id: 1,
      name: "Nha Trang",
      image:
        "https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=400&h=300&fit=crop",
      province_id: 1,
    },
    {
      id: 2,
      name: "Ho Chi Minh City",
      image:
        "https://images.unsplash.com/photo-1583417319070-4a69db38a482?w=400&h=300&fit=crop",
      province_id: 2,
    },
    {
      id: 3,
      name: "Da Nang",
      image:
        "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop",
      province_id: 3,
    },
  ];

  const handlePropertyTypeClick = (type) => {
    navigate(`/booking-rooms?room_type=${encodeURIComponent(type)}`);
  };

  const handleDestinationClick = (destination) => {
    navigate(`/booking-rooms?search=${encodeURIComponent(destination.name)}`);
  };

  return (
    <Layout>
      {/* Hero Section with Search */}
      <BookingHeroSection />

      {/* Main Content */}
      <div
        style={{
          maxWidth: "1200px",
          margin: "0 auto",
          padding: "40px 24px",
          backgroundColor: "#fff",
        }}
      >
        {/* Browse by property type section */}
        <div style={{ marginBottom: "48px" }}>
          <Title
            level={2}
            style={{
              margin: "0 0 24px 0",
              fontSize: "24px",
              fontWeight: "600",
              color: "#262626",
            }}
          >
            Browse by property type in Nha Trang
          </Title>

          <Row gutter={[16, 16]}>
            {propertyTypes.map((property) => (
              <Col xs={24} sm={12} md={6} key={property.id}>
                <Card
                  hoverable
                  style={{
                    borderRadius: "8px",
                    overflow: "hidden",
                    border: "1px solid #e8e8e8",
                    cursor: "pointer",
                  }}
                  styles={{ body: { padding: 0 } }}
                  onClick={() => handlePropertyTypeClick(property.type)}
                >
                  <div style={{ position: "relative" }}>
                    <img
                      src={property.image}
                      alt={property.title}
                      style={{
                        width: "100%",
                        height: "160px",
                        objectFit: "cover",
                      }}
                    />
                  </div>
                  <div style={{ padding: "12px 16px" }}>
                    <Title
                      level={5}
                      style={{
                        margin: "0 0 4px 0",
                        fontSize: "16px",
                        fontWeight: "600",
                        color: "#262626",
                      }}
                    >
                      {property.title}
                    </Title>
                    <Text
                      style={{
                        fontSize: "12px",
                        color: "#6b6b6b",
                        display: "block",
                        marginBottom: "4px",
                      }}
                    >
                      {property.subtitle}
                    </Text>
                    <Text
                      style={{
                        fontSize: "12px",
                        color: "#6b6b6b",
                      }}
                    >
                      {property.available}
                    </Text>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </div>

        {/* Trending destinations section */}
        <div>
          <Title
            level={2}
            style={{
              margin: "0 0 8px 0",
              fontSize: "24px",
              fontWeight: "600",
              color: "#262626",
            }}
          >
            Trending destinations
          </Title>
          <Text
            style={{
              fontSize: "14px",
              color: "#6b6b6b",
              display: "block",
              marginBottom: "24px",
            }}
          >
            Travellers searching for Vietnam also booked these
          </Text>

          <Row gutter={[16, 16]}>
            {trendingDestinations.map((destination) => (
              <Col xs={24} sm={12} md={8} key={destination.id}>
                <Card
                  hoverable
                  style={{
                    borderRadius: "8px",
                    overflow: "hidden",
                    border: "1px solid #e8e8e8",
                    cursor: "pointer",
                  }}
                  styles={{ body: { padding: 0 } }}
                  onClick={() => handleDestinationClick(destination)}
                >
                  <div style={{ position: "relative" }}>
                    <img
                      src={destination.image}
                      alt={destination.name}
                      style={{
                        width: "100%",
                        height: "200px",
                        objectFit: "cover",
                      }}
                    />
                    <div
                      style={{
                        position: "absolute",
                        bottom: "16px",
                        left: "16px",
                        color: "white",
                      }}
                    >
                      <Title
                        level={3}
                        style={{
                          margin: 0,
                          fontSize: "24px",
                          fontWeight: "600",
                          color: "white",
                          textShadow: "0 2px 4px rgba(0,0,0,0.5)",
                        }}
                      >
                        {destination.name}
                      </Title>
                    </div>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      </div>
    </Layout>
  );
};

export default Home;
