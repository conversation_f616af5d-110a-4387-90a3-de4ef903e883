import React, { useState, useEffect } from "react";
import {
  Row,
  Col,
  Typography,
  Spin,
  Empty,
  Pagination,
  Button,
  Space,
} from "antd";
import { FilterOutlined } from "@ant-design/icons";
import Layout from "../components/layout/Layout";
import BookingHeroSection from "../components/common/BookingHeroSection";
import BookingRoomCard from "../components/room/BookingRoomCard";
import { roomAPI } from "../services/api";

const { Title, Text } = Typography;

const Home = () => {
  const [rooms, setRooms] = useState([]);
  const [filteredRooms, setFilteredRooms] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(8);
  const [totalRooms, setTotalRooms] = useState(0);
  const [currentFilters, setCurrentFilters] = useState({});

  useEffect(() => {
    fetchRooms(currentFilters);
  }, [currentPage]);

  // Separate effect for initial load
  useEffect(() => {
    if (Object.keys(currentFilters).length === 0) {
      fetchRooms();
    }
  }, []);

  const fetchRooms = async (searchParams = {}) => {
    try {
      setLoading(true);

      const params = {
        page: currentPage,
        limit: pageSize,
        ...searchParams,
      };

      console.log("🏠 Fetching rooms with params:", params);

      const response = await roomAPI.getAllRooms(params);

      console.log("📥 Rooms API response:", response);

      if (response.success) {
        setRooms(response.data.rooms);
        setFilteredRooms(response.data.rooms);
        setTotalRooms(response.data.pagination.total_items);
      } else {
        console.error("API returned error:", response.message);
        setRooms([]);
        setFilteredRooms([]);
        setTotalRooms(0);
      }
    } catch (error) {
      console.error("Error fetching rooms:", error);
      setRooms([]);
      setFilteredRooms([]);
      setTotalRooms(0);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async (searchFilters) => {
    console.log("🔍 Home search filters:", searchFilters);
    console.log("📊 Current filters before update:", currentFilters);

    // Convert date objects to strings if they exist
    const processedFilters = { ...searchFilters };
    if (processedFilters.check_in) {
      processedFilters.check_in =
        processedFilters.check_in.format("YYYY-MM-DD");
    }
    if (processedFilters.check_out) {
      processedFilters.check_out =
        processedFilters.check_out.format("YYYY-MM-DD");
    }

    // Save current filters
    setCurrentFilters(processedFilters);
    console.log("📊 Processed filters:", processedFilters);

    // Reset to first page when searching
    setCurrentPage(1);

    // Fetch rooms with search parameters
    await fetchRooms(processedFilters);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  if (loading) {
    return (
      <Layout>
        <div style={{ textAlign: "center", padding: "100px 0" }}>
          <Spin size="large" />
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      {/* Hero Section with Search */}
      <BookingHeroSection loading={loading} initialValues={currentFilters} />

      {/* Main Content */}
      <div
        style={{
          maxWidth: "1200px",
          margin: "0 auto",
          padding: "32px 24px",
          backgroundColor: "#f5f5f5",
          minHeight: "calc(100vh - 70px)",
        }}
      >
        {/* Section Header */}
        <div style={{ marginBottom: "24px" }}>
          <Title
            level={2}
            style={{
              margin: "0 0 8px 0",
              fontSize: "28px",
              fontWeight: "700",
              color: "#003580",
            }}
          >
            {filteredRooms.length > 0
              ? `${filteredRooms.length} chỗ ở được tìm thấy`
              : "Chỗ ở phổ biến"}
          </Title>
          <Text style={{ fontSize: "16px", color: "#666" }}>
            Khám phá những chỗ ở tuyệt vời tại Việt Nam
          </Text>
        </div>

        {/* Filters */}
        <div style={{ marginBottom: "24px" }}>
          <Space wrap>
            <Button
              icon={<FilterOutlined />}
              style={{
                border: "1px solid #0071c2",
                borderRadius: "4px",
                height: "40px",
                color: "#0071c2",
                fontWeight: "500",
              }}
            >
              Bộ lọc
            </Button>
            <Button
              style={{
                border: "1px solid #ddd",
                borderRadius: "4px",
                height: "40px",
                color: "#666",
              }}
            >
              Giá: Thấp đến cao
            </Button>
            <Button
              style={{
                border: "1px solid #ddd",
                borderRadius: "4px",
                height: "40px",
                color: "#666",
              }}
            >
              Đánh giá và điểm số
            </Button>
          </Space>
        </div>

        {/* Rooms List */}
        {filteredRooms.length === 0 ? (
          <Empty
            description="Không tìm thấy chỗ ở nào phù hợp"
            style={{
              padding: "100px 0",
              backgroundColor: "#fff",
              borderRadius: "8px",
            }}
          />
        ) : (
          <>
            <Row gutter={[16, 16]}>
              {filteredRooms.map((room) => (
                <Col xs={24} sm={24} md={12} lg={8} xl={8} key={room.id}>
                  <BookingRoomCard room={room} />
                </Col>
              ))}
            </Row>

            {/* Pagination */}
            {totalRooms > pageSize && (
              <div
                style={{
                  textAlign: "center",
                  marginTop: "48px",
                  paddingTop: "24px",
                  borderTop: "1px solid #ddd",
                  backgroundColor: "#fff",
                  borderRadius: "8px",
                  padding: "24px",
                }}
              >
                <Pagination
                  current={currentPage}
                  total={totalRooms}
                  pageSize={pageSize}
                  onChange={handlePageChange}
                  showSizeChanger={false}
                  showQuickJumper
                  showTotal={(total, range) =>
                    `Hiển thị ${range[0]}-${range[1]} trong tổng số ${total} chỗ ở`
                  }
                  style={{
                    "& .ant-pagination-item-active": {
                      backgroundColor: "#0071c2",
                      borderColor: "#0071c2",
                    },
                  }}
                />
              </div>
            )}
          </>
        )}
      </div>
    </Layout>
  );
};

export default Home;
