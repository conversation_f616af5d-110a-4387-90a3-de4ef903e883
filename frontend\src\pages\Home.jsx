import React, { useState, useEffect } from "react";
import {
  Row,
  Col,
  Typography,
  Spin,
  Empty,
  Pagination,
  Button,
  Space,
} from "antd";
import { FilterOutlined, AppstoreOutlined } from "@ant-design/icons";
import Layout from "../components/layout/Layout";
import AirbnbSearchBar from "../components/common/AirbnbSearchBar";
import CategoryFilter from "../components/common/CategoryFilter";
import AirbnbRoomCard from "../components/room/AirbnbRoomCard";
import { roomAPI } from "../services/api";

const { Title, Text } = Typography;

const Home = () => {
  const [rooms, setRooms] = useState([]);
  const [filteredRooms, setFilteredRooms] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(8);
  const [totalRooms, setTotalRooms] = useState(0);
  const [currentFilters, setCurrentFilters] = useState({});
  const [activeCategory, setActiveCategory] = useState(null);

  useEffect(() => {
    fetchRooms(currentFilters);
  }, [currentPage]);

  // Separate effect for initial load
  useEffect(() => {
    if (Object.keys(currentFilters).length === 0) {
      fetchRooms();
    }
  }, []);

  const fetchRooms = async (searchParams = {}) => {
    try {
      setLoading(true);

      const params = {
        page: currentPage,
        limit: pageSize,
        ...searchParams,
      };

      console.log("🏠 Fetching rooms with params:", params);

      const response = await roomAPI.getAllRooms(params);

      console.log("📥 Rooms API response:", response);

      if (response.success) {
        setRooms(response.data.rooms);
        setFilteredRooms(response.data.rooms);
        setTotalRooms(response.data.pagination.total_items);
      } else {
        console.error("API returned error:", response.message);
        setRooms([]);
        setFilteredRooms([]);
        setTotalRooms(0);
      }
    } catch (error) {
      console.error("Error fetching rooms:", error);
      setRooms([]);
      setFilteredRooms([]);
      setTotalRooms(0);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async (searchFilters) => {
    console.log("🔍 Home search filters:", searchFilters);
    console.log("📊 Current filters before update:", currentFilters);

    // Save current filters
    setCurrentFilters(searchFilters);
    console.log("📊 Current filters after update:", searchFilters);

    // Reset to first page when searching
    setCurrentPage(1);

    // Fetch rooms with search parameters
    await fetchRooms(searchFilters);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const handleCategoryChange = (category) => {
    setActiveCategory(category);
    const newFilters = { ...currentFilters };
    if (category) {
      newFilters.room_type = category;
    } else {
      delete newFilters.room_type;
    }
    setCurrentFilters(newFilters);
    setCurrentPage(1);
    fetchRooms(newFilters);
  };

  if (loading) {
    return (
      <Layout>
        <div style={{ textAlign: "center", padding: "100px 0" }}>
          <Spin size="large" />
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      {/* Hero Search Section */}
      <AirbnbSearchBar
        onSearch={handleSearch}
        loading={loading}
        initialValues={currentFilters}
        key={JSON.stringify(currentFilters)}
      />

      {/* Main Content */}
      <div
        style={{
          maxWidth: "1760px",
          margin: "0 auto",
          padding: "24px",
        }}
      >
        {/* Section Header */}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "24px",
          }}
        >
          <div>
            <Title
              level={2}
              style={{ margin: 0, fontSize: "32px", fontWeight: "600" }}
            >
              Chỗ ở tại Việt Nam
            </Title>
            <Text style={{ fontSize: "16px", color: "#717171" }}>
              {filteredRooms.length} chỗ ở
            </Text>
          </div>

          <Space>
            <Button
              icon={<FilterOutlined />}
              style={{
                border: "1px solid #ddd",
                borderRadius: "8px",
                height: "48px",
                padding: "0 16px",
                display: "flex",
                alignItems: "center",
                gap: "8px",
              }}
            >
              Bộ lọc
            </Button>

            <Button
              icon={<AppstoreOutlined />}
              style={{
                border: "1px solid #ddd",
                borderRadius: "8px",
                height: "48px",
                width: "48px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            />
          </Space>
        </div>

        {/* Rooms Grid */}
        {filteredRooms.length === 0 ? (
          <Empty
            description="Không tìm thấy chỗ ở nào phù hợp"
            style={{ padding: "100px 0" }}
          />
        ) : (
          <>
            <Row gutter={[24, 40]}>
              {filteredRooms.map((room) => (
                <Col xs={24} sm={12} md={8} lg={6} xl={4} key={room.id}>
                  <AirbnbRoomCard room={room} />
                </Col>
              ))}
            </Row>

            {/* Pagination */}
            {totalRooms > pageSize && (
              <div
                style={{
                  textAlign: "center",
                  marginTop: "64px",
                  paddingTop: "32px",
                  borderTop: "1px solid #e8e8e8",
                }}
              >
                <Pagination
                  current={currentPage}
                  total={totalRooms}
                  pageSize={pageSize}
                  onChange={handlePageChange}
                  showSizeChanger={false}
                  showQuickJumper
                  showTotal={(total, range) =>
                    `${range[0]}-${range[1]} của ${total} chỗ ở`
                  }
                />
              </div>
            )}
          </>
        )}
      </div>
    </Layout>
  );
};

export default Home;
