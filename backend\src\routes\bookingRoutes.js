const express = require('express');
const router = express.Router();
const bookingController = require('../controllers/bookingController');
const { authenticateToken } = require('../middleware/auth');

// All booking routes require authentication
router.use(authenticateToken);

// Create a new booking
router.post('/', bookingController.createBooking);

// Get user's bookings
router.get('/my-bookings', bookingController.getUserBookings);

// Check room availability
router.get('/check-availability', bookingController.checkAvailability);

// Get booking by ID
router.get('/:id', bookingController.getBookingById);

// Cancel booking
router.patch('/:id/cancel', bookingController.cancelBooking);

module.exports = router;
