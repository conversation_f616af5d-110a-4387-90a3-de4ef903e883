import React, { useState, useEffect } from 'react';
import { Input, Select, Button, DatePicker, Space, Dropdown, Card } from 'antd';
import { SearchOutlined, CalendarOutlined, UserOutlined } from '@ant-design/icons';
import { addressAPI } from '../../services/api';

const { Option } = Select;
const { RangePicker } = DatePicker;

const AirbnbSearchBar = ({ onSearch, loading, style, initialValues = {} }) => {
  const [searchForm, setSearchForm] = useState({
    search: initialValues.search || '',
    province_id: initialValues.province_id || undefined,
    district_id: initialValues.district_id || undefined,
    ward_id: initialValues.ward_id || undefined,
    room_type: initialValues.room_type || undefined,
    min_price: initialValues.min_price || undefined,
    max_price: initialValues.max_price || undefined
  });

  const [provinces, setProvinces] = useState([]);
  const [districts, setDistricts] = useState([]);
  const [wards, setWards] = useState([]);
  const [loadingAddress, setLoadingAddress] = useState(false);
  const [activeField, setActiveField] = useState(null);

  const roomTypes = [
    'Phòng trọ',
    'Chung cư', 
    'Nhà riêng',
    'Khách sạn',
    'Homestay'
  ];

  useEffect(() => {
    fetchProvinces();
  }, []);

  const fetchProvinces = async () => {
    try {
      setLoadingAddress(true);
      const response = await addressAPI.getProvinces();
      if (response.success) {
        setProvinces(response.data.provinces);
      }
    } catch (error) {
      console.error('Error fetching provinces:', error);
    } finally {
      setLoadingAddress(false);
    }
  };

  const fetchDistricts = async (provinceId) => {
    try {
      setLoadingAddress(true);
      const response = await addressAPI.getDistricts(provinceId);
      if (response.success) {
        setDistricts(response.data.districts);
        return response.data.districts;
      }
    } catch (error) {
      console.error('Error fetching districts:', error);
    } finally {
      setLoadingAddress(false);
    }
    return [];
  };

  const fetchWards = async (districtId) => {
    try {
      setLoadingAddress(true);
      const response = await addressAPI.getWards(districtId);
      if (response.success) {
        setWards(response.data.wards);
        return response.data.wards;
      }
    } catch (error) {
      console.error('Error fetching wards:', error);
    } finally {
      setLoadingAddress(false);
    }
    return [];
  };

  const handleProvinceChange = (value) => {
    setSearchForm(prev => ({
      ...prev,
      province_id: value,
      district_id: undefined,
      ward_id: undefined
    }));
    setDistricts([]);
    setWards([]);

    if (value) {
      fetchDistricts(value);
    }
  };

  const handleDistrictChange = (value) => {
    setSearchForm(prev => ({
      ...prev,
      district_id: value,
      ward_id: undefined
    }));
    setWards([]);

    if (value) {
      fetchWards(value);
    }
  };

  const handleInputChange = (field, value) => {
    setSearchForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSearch = () => {
    const cleanFilters = Object.fromEntries(
      Object.entries(searchForm).filter(([_, value]) => value !== undefined && value !== '')
    );
    onSearch(cleanFilters);
    setActiveField(null);
  };

  const getLocationText = () => {
    if (searchForm.search) return searchForm.search;
    
    const province = provinces.find(p => p.id === searchForm.province_id);
    const district = districts.find(d => d.id === searchForm.district_id);
    const ward = wards.find(w => w.id === searchForm.ward_id);
    
    if (ward) return `${ward.name}, ${district?.name}, ${province?.name}`;
    if (district) return `${district.name}, ${province?.name}`;
    if (province) return province.name;
    
    return 'Bạn sắp đi đâu?';
  };

  const LocationDropdown = () => (
    <Card style={{ width: 400, padding: '16px' }}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <Input
          placeholder="Tìm kiếm theo tên phòng, địa chỉ..."
          value={searchForm.search}
          onChange={(e) => handleInputChange('search', e.target.value)}
          style={{ marginBottom: '12px' }}
        />
        
        <Select
          placeholder="Tỉnh/Thành phố"
          value={searchForm.province_id}
          onChange={handleProvinceChange}
          loading={loadingAddress}
          allowClear
          showSearch
          style={{ width: '100%' }}
        >
          {provinces.map(province => (
            <Option key={province.id} value={province.id}>
              {province.name}
            </Option>
          ))}
        </Select>

        <Select
          placeholder="Quận/Huyện"
          value={searchForm.district_id}
          onChange={handleDistrictChange}
          loading={loadingAddress}
          allowClear
          showSearch
          style={{ width: '100%' }}
          disabled={!searchForm.province_id}
        >
          {districts.map(district => (
            <Option key={district.id} value={district.id}>
              {district.name}
            </Option>
          ))}
        </Select>

        <Select
          placeholder="Phường/Xã"
          value={searchForm.ward_id}
          onChange={(value) => handleInputChange('ward_id', value)}
          loading={loadingAddress}
          allowClear
          showSearch
          style={{ width: '100%' }}
          disabled={!searchForm.district_id}
        >
          {wards.map(ward => (
            <Option key={ward.id} value={ward.id}>
              {ward.name}
            </Option>
          ))}
        </Select>
      </Space>
    </Card>
  );

  const TypeDropdown = () => (
    <Card style={{ width: 300, padding: '16px' }}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <div style={{ fontWeight: '600', marginBottom: '12px' }}>Loại chỗ ở</div>
        <Select
          placeholder="Chọn loại phòng"
          value={searchForm.room_type}
          onChange={(value) => handleInputChange('room_type', value)}
          allowClear
          style={{ width: '100%' }}
        >
          {roomTypes.map(type => (
            <Option key={type} value={type}>
              {type}
            </Option>
          ))}
        </Select>
      </Space>
    </Card>
  );

  return (
    <div style={{ 
      padding: '24px',
      backgroundColor: '#fff',
      borderBottom: '1px solid #e8e8e8',
      ...style 
    }}>
      <div style={{ 
        maxWidth: '850px',
        margin: '0 auto',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{
          display: 'flex',
          border: '1px solid #ddd',
          borderRadius: '32px',
          backgroundColor: '#fff',
          boxShadow: '0 1px 2px rgba(0,0,0,0.08), 0 4px 12px rgba(0,0,0,0.05)',
          overflow: 'hidden'
        }}>
          {/* Location */}
          <Dropdown
            overlay={<LocationDropdown />}
            trigger={['click']}
            open={activeField === 'location'}
            onOpenChange={(open) => setActiveField(open ? 'location' : null)}
            placement="bottomLeft"
          >
            <div style={{
              padding: '14px 24px',
              borderRight: '1px solid #ddd',
              cursor: 'pointer',
              minWidth: '200px',
              backgroundColor: activeField === 'location' ? '#f7f7f7' : 'transparent'
            }}>
              <div style={{ fontSize: '12px', fontWeight: '600', color: '#222' }}>
                Địa điểm
              </div>
              <div style={{ 
                fontSize: '14px', 
                color: getLocationText() === 'Bạn sắp đi đâu?' ? '#717171' : '#222',
                marginTop: '2px'
              }}>
                {getLocationText()}
              </div>
            </div>
          </Dropdown>

          {/* Check-in */}
          <div style={{
            padding: '14px 24px',
            borderRight: '1px solid #ddd',
            cursor: 'pointer',
            minWidth: '120px'
          }}>
            <div style={{ fontSize: '12px', fontWeight: '600', color: '#222' }}>
              Nhận phòng
            </div>
            <div style={{ fontSize: '14px', color: '#717171', marginTop: '2px' }}>
              Thêm ngày
            </div>
          </div>

          {/* Check-out */}
          <div style={{
            padding: '14px 24px',
            borderRight: '1px solid #ddd',
            cursor: 'pointer',
            minWidth: '120px'
          }}>
            <div style={{ fontSize: '12px', fontWeight: '600', color: '#222' }}>
              Trả phòng
            </div>
            <div style={{ fontSize: '14px', color: '#717171', marginTop: '2px' }}>
              Thêm ngày
            </div>
          </div>

          {/* Type */}
          <Dropdown
            overlay={<TypeDropdown />}
            trigger={['click']}
            open={activeField === 'type'}
            onOpenChange={(open) => setActiveField(open ? 'type' : null)}
            placement="bottomRight"
          >
            <div style={{
              padding: '14px 24px',
              cursor: 'pointer',
              minWidth: '120px',
              backgroundColor: activeField === 'type' ? '#f7f7f7' : 'transparent',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}>
              <div>
                <div style={{ fontSize: '12px', fontWeight: '600', color: '#222' }}>
                  Loại phòng
                </div>
                <div style={{ 
                  fontSize: '14px', 
                  color: searchForm.room_type ? '#222' : '#717171',
                  marginTop: '2px'
                }}>
                  {searchForm.room_type || 'Thêm loại'}
                </div>
              </div>
              
              <Button
                type="primary"
                shape="circle"
                icon={<SearchOutlined />}
                size="large"
                onClick={handleSearch}
                loading={loading}
                style={{
                  backgroundColor: '#FF385C',
                  borderColor: '#FF385C',
                  width: '48px',
                  height: '48px',
                  marginLeft: '12px'
                }}
              />
            </div>
          </Dropdown>
        </div>
      </div>
    </div>
  );
};

export default AirbnbSearchBar;
