const { Room, User, Address, Ward, District, Province, RoomImage, Booking } = require('../models');
const { cache } = require('../config/redis');
const { Op } = require('sequelize');

const getAllRooms = async (req, res, next) => {
  try {
    console.log('🏠 getAllRooms called with query:', req.query);

    const {
      page = 1,
      limit = 10,
      search,
      place_id,
      room_type,
      min_price,
      max_price,
      province_id,
      district_id,
      ward_id,
      check_in,
      check_out,
      guests,
      sort_by = 'created_at',
      sort_order = 'DESC'
    } = req.query;

    // Create cache key
    const cacheKey = `rooms:${JSON.stringify(req.query)}`;

    // Try to get from cache first
    const cachedData = await cache.get(cacheKey);
    if (cachedData) {
      return res.json({
        success: true,
        data: cachedData,
        cached: true
      });
    }

    // Build where conditions for rooms
    const whereConditions = {};
    const includeConditions = [];

    // Search in title, description, and address
    if (search) {
      whereConditions[Op.or] = [
        { title: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } }
      ];
    }

    if (room_type) {
      whereConditions.room_type = room_type;
    }

    // Filter by number of guests
    if (guests) {
      whereConditions.max_guests = { [Op.gte]: parseInt(guests) };
    }

    if (min_price || max_price) {
      whereConditions.price = {};
      if (min_price) whereConditions.price[Op.gte] = min_price;
      if (max_price) whereConditions.price[Op.lte] = max_price;
    }

    // Filter by availability dates
    if (check_in && check_out) {
      console.log('🗓️ Filtering by dates:', { check_in, check_out });

      // Find rooms that are NOT booked during the requested period
      const conflictingBookings = await Booking.findAll({
        where: {
          status: { [Op.in]: ['confirmed', 'pending'] },
          [Op.or]: [
            // Booking starts during requested period
            {
              check_in_date: {
                [Op.between]: [check_in, check_out]
              }
            },
            // Booking ends during requested period
            {
              check_out_date: {
                [Op.between]: [check_in, check_out]
              }
            },
            // Booking encompasses the entire requested period
            {
              check_in_date: { [Op.lte]: check_in },
              check_out_date: { [Op.gte]: check_out }
            }
          ]
        },
        attributes: ['room_id']
      });

      const bookedRoomIds = conflictingBookings.map(booking => booking.room_id);

      if (bookedRoomIds.length > 0) {
        whereConditions.id = { [Op.notIn]: bookedRoomIds };
      }

      console.log('🚫 Excluded booked rooms:', bookedRoomIds);
    }

    // Build address filter conditions
    let addressWhere = {};
    let wardWhere = {};
    let districtWhere = {};
    let provinceWhere = {};

    if (ward_id) {
      wardWhere.id = ward_id;
    }
    if (district_id) {
      districtWhere.id = district_id;
    }
    if (province_id) {
      provinceWhere.id = province_id;
    }

    // Add street_detail search to address conditions
    if (search) {
      addressWhere[Op.or] = [
        ...(addressWhere[Op.or] || []),
        { street_detail: { [Op.like]: `%${search}%` } }
      ];
    }

    const offset = (page - 1) * limit;

    const { count, rows } = await Room.findAndCountAll({
      where: whereConditions,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name', 'email', 'phone']
        },
        {
          model: Address,
          as: 'address',
          required: Object.keys(addressWhere).length > 0 || Object.keys(wardWhere).length > 0 || Object.keys(districtWhere).length > 0 || Object.keys(provinceWhere).length > 0,
          where: Object.keys(addressWhere).length > 0 ? addressWhere : undefined,
          include: [
            {
              model: Ward,
              as: 'ward',
              required: Object.keys(wardWhere).length > 0 || Object.keys(districtWhere).length > 0 || Object.keys(provinceWhere).length > 0,
              where: Object.keys(wardWhere).length > 0 ? wardWhere : undefined,
              include: [
                {
                  model: District,
                  as: 'district',
                  required: Object.keys(districtWhere).length > 0 || Object.keys(provinceWhere).length > 0,
                  where: Object.keys(districtWhere).length > 0 ? districtWhere : undefined,
                  include: [
                    {
                      model: Province,
                      as: 'province',
                      required: Object.keys(provinceWhere).length > 0,
                      where: Object.keys(provinceWhere).length > 0 ? provinceWhere : undefined
                    }
                  ]
                }
              ]
            }
          ]
        },
        {
          model: RoomImage,
          as: 'images',
          limit: 5
        }
      ],
      order: [[sort_by, sort_order.toUpperCase()]],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    const result = {
      rooms: rows,
      pagination: {
        current_page: parseInt(page),
        total_pages: Math.ceil(count / limit),
        total_items: count,
        items_per_page: parseInt(limit)
      }
    };

    // Cache the result for 10 minutes
    await cache.set(cacheKey, result, 600);

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    next(error);
  }
};

const getRoomById = async (req, res, next) => {
  try {
    const { id } = req.params;

    const room = await Room.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name', 'email', 'phone']
        },
        {
          model: Address,
          as: 'address',
          include: [
            {
              model: Ward,
              as: 'ward',
              include: [
                {
                  model: District,
                  as: 'district',
                  include: [
                    {
                      model: Province,
                      as: 'province'
                    }
                  ]
                }
              ]
            }
          ]
        },
        {
          model: RoomImage,
          as: 'images'
        }
      ]
    });

    if (!room) {
      return res.status(404).json({
        success: false,
        message: 'Room not found'
      });
    }

    res.json({
      success: true,
      data: { room }
    });
  } catch (error) {
    next(error);
  }
};

const createRoom = async (req, res, next) => {
  try {
    const {
      title,
      description,
      ward_id,
      street_detail,
      lat,
      lng,
      price,
      area,
      room_type,
      images
    } = req.body;

    // Create address first
    const address = await Address.create({
      ward_id,
      street_detail,
      lat,
      lng
    });

    const room = await Room.create({
      user_id: req.user.id,
      address_id: address.id,
      title,
      description,
      price,
      area,
      room_type
    });

    // Add images if provided
    if (images && images.length > 0) {
      const imagePromises = images.map((image, index) =>
        RoomImage.create({
          room_id: room.id,
          image_url: image.url,
          caption: image.caption || '',
        })
      );
      await Promise.all(imagePromises);
    }

    // Clear cache
    await cache.del('rooms:*');

    const roomWithDetails = await Room.findByPk(room.id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name', 'email']
        },
        {
          model: Address,
          as: 'address',
          include: [
            {
              model: Ward,
              as: 'ward',
              include: [
                {
                  model: District,
                  as: 'district',
                  include: [
                    {
                      model: Province,
                      as: 'province'
                    }
                  ]
                }
              ]
            }
          ]
        },
        {
          model: RoomImage,
          as: 'images'
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: 'Room created successfully',
      data: { room: roomWithDetails }
    });
  } catch (error) {
    next(error);
  }
};

const updateRoom = async (req, res, next) => {
  try {
    const { id } = req.params;
    const {
      title,
      description,
      address,
      lat,
      lng,
      price,
      area,
      room_type,
      place_id
    } = req.body;

    const room = await Room.findByPk(id);

    if (!room) {
      return res.status(404).json({
        success: false,
        message: 'Room not found'
      });
    }

    // Check if user owns the room or is admin
    if (room.user_id !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to update this room'
      });
    }

    await room.update({
      title: title || room.title,
      description: description || room.description,
      address: address || room.address,
      lat: lat || room.lat,
      lng: lng || room.lng,
      price: price || room.price,
      area: area || room.area,
      room_type: room_type || room.room_type,
      place_id: place_id || room.place_id
    });

    // Clear cache
    await cache.del('rooms:*');

    const updatedRoom = await Room.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name', 'email']
        },
        {
          model: Address,
          as: 'address',
          include: [
            {
              model: Ward,
              as: 'ward',
              include: [
                {
                  model: District,
                  as: 'district',
                  include: [
                    {
                      model: Province,
                      as: 'province'
                    }
                  ]
                }
              ]
            }
          ]
        },
        {
          model: RoomImage,
          as: 'images'
        }
      ]
    });

    res.json({
      success: true,
      message: 'Room updated successfully',
      data: { room: updatedRoom }
    });
  } catch (error) {
    next(error);
  }
};

const deleteRoom = async (req, res, next) => {
  try {
    const { id } = req.params;

    const room = await Room.findByPk(id);

    if (!room) {
      return res.status(404).json({
        success: false,
        message: 'Room not found'
      });
    }

    // Check if user owns the room or is admin
    if (room.user_id !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to delete this room'
      });
    }

    await room.destroy();

    // Clear cache
    await cache.del('rooms:*');

    res.json({
      success: true,
      message: 'Room deleted successfully'
    });
  } catch (error) {
    next(error);
  }
};

const getUserRooms = async (req, res, next) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    const { count, rows } = await Room.findAndCountAll({
      where: { user_id: req.user.id },
      include: [
        {
          model: Address,
          as: 'address',
          include: [
            {
              model: Ward,
              as: 'ward',
              include: [
                {
                  model: District,
                  as: 'district',
                  include: [
                    {
                      model: Province,
                      as: 'province'
                    }
                  ]
                }
              ]
            }
          ]
        },
        {
          model: RoomImage,
          as: 'images',
          limit: 3
        }
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    const result = {
      rooms: rows,
      pagination: {
        current_page: parseInt(page),
        total_pages: Math.ceil(count / limit),
        total_items: count,
        items_per_page: parseInt(limit)
      }
    };

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getAllRooms,
  getRoomById,
  createRoom,
  updateRoom,
  deleteRoom,
  getUserRooms
};
