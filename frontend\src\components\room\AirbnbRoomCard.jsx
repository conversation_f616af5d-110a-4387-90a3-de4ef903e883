import React, { useState } from 'react';
import { Card, Typography, Space, Button, Image, Rate } from 'antd';
import {
  HeartOutlined,
  HeartFilled,
  StarFilled
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { formatPrice } from '../../utils/formatters';

const { Text } = Typography;

const AirbnbRoomCard = ({ room, showActions = true }) => {
  const navigate = useNavigate();
  const [isFavorite, setIsFavorite] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Get data from room object
  const images = room.images || [];
  const user = room.user || {};
  const address = room.address || {};
  
  // Get primary image or fallback
  const imageUrl = images[currentImageIndex]?.image_url || 
                   images[0]?.image_url || 
                   'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=500';

  const getLocationText = () => {
    const ward = address.ward?.name;
    const district = address.ward?.district?.name;
    const province = address.ward?.district?.province?.name;
    
    if (ward && district) {
      return `${ward}, ${district}`;
    }
    if (district && province) {
      return `${district}, ${province}`;
    }
    if (province) {
      return province;
    }
    return 'Việt Nam';
  };

  const handleViewDetail = () => {
    navigate(`/rooms/${room.id}`);
  };

  const handleFavoriteClick = (e) => {
    e.stopPropagation();
    setIsFavorite(!isFavorite);
  };

  const handleImageNavigation = (direction, e) => {
    e.stopPropagation();
    if (direction === 'next' && currentImageIndex < images.length - 1) {
      setCurrentImageIndex(currentImageIndex + 1);
    } else if (direction === 'prev' && currentImageIndex > 0) {
      setCurrentImageIndex(currentImageIndex - 1);
    }
  };

  return (
    <div 
      style={{ 
        cursor: 'pointer',
        marginBottom: '40px'
      }}
      onClick={handleViewDetail}
    >
      {/* Image Container */}
      <div style={{ 
        position: 'relative',
        borderRadius: '12px',
        overflow: 'hidden',
        marginBottom: '12px',
        aspectRatio: '1 / 1'
      }}>
        <Image
          src={imageUrl}
          alt={room.title}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover'
          }}
          preview={false}
        />
        
        {/* Favorite Button */}
        <Button
          type="text"
          icon={isFavorite ? <HeartFilled /> : <HeartOutlined />}
          onClick={handleFavoriteClick}
          style={{
            position: 'absolute',
            top: '12px',
            right: '12px',
            color: isFavorite ? '#FF385C' : '#fff',
            backgroundColor: 'rgba(0,0,0,0.3)',
            border: 'none',
            borderRadius: '50%',
            width: '32px',
            height: '32px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        />

        {/* Image Navigation */}
        {images.length > 1 && (
          <>
            {currentImageIndex > 0 && (
              <Button
                type="text"
                onClick={(e) => handleImageNavigation('prev', e)}
                style={{
                  position: 'absolute',
                  left: '12px',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  backgroundColor: 'rgba(255,255,255,0.9)',
                  border: 'none',
                  borderRadius: '50%',
                  width: '32px',
                  height: '32px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '16px'
                }}
              >
                ‹
              </Button>
            )}
            
            {currentImageIndex < images.length - 1 && (
              <Button
                type="text"
                onClick={(e) => handleImageNavigation('next', e)}
                style={{
                  position: 'absolute',
                  right: '12px',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  backgroundColor: 'rgba(255,255,255,0.9)',
                  border: 'none',
                  borderRadius: '50%',
                  width: '32px',
                  height: '32px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '16px'
                }}
              >
                ›
              </Button>
            )}

            {/* Image Dots */}
            <div style={{
              position: 'absolute',
              bottom: '12px',
              left: '50%',
              transform: 'translateX(-50%)',
              display: 'flex',
              gap: '4px'
            }}>
              {images.map((_, index) => (
                <div
                  key={index}
                  style={{
                    width: '6px',
                    height: '6px',
                    borderRadius: '50%',
                    backgroundColor: index === currentImageIndex ? '#fff' : 'rgba(255,255,255,0.5)'
                  }}
                />
              ))}
            </div>
          </>
        )}
      </div>

      {/* Content */}
      <div style={{ padding: '0 4px' }}>
        {/* Location and Rating */}
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'flex-start',
          marginBottom: '4px'
        }}>
          <Text style={{ 
            fontSize: '15px', 
            fontWeight: '600', 
            color: '#222',
            lineHeight: '18px'
          }}>
            {getLocationText()}
          </Text>
          
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
            <StarFilled style={{ fontSize: '12px', color: '#222' }} />
            <Text style={{ fontSize: '14px', color: '#222' }}>
              4.8
            </Text>
          </div>
        </div>

        {/* Host */}
        <Text style={{ 
          fontSize: '15px', 
          color: '#717171',
          lineHeight: '18px',
          display: 'block',
          marginBottom: '4px'
        }}>
          Chủ nhà: {user.name || 'Ẩn danh'}
        </Text>

        {/* Room Type */}
        <Text style={{ 
          fontSize: '15px', 
          color: '#717171',
          lineHeight: '18px',
          display: 'block',
          marginBottom: '8px'
        }}>
          {room.room_type}
        </Text>

        {/* Price */}
        <div style={{ display: 'flex', alignItems: 'baseline', gap: '4px' }}>
          <Text style={{ 
            fontSize: '16px', 
            fontWeight: '600', 
            color: '#222'
          }}>
            {formatPrice(room.price)}
          </Text>
          <Text style={{ 
            fontSize: '16px', 
            color: '#222'
          }}>
            / tháng
          </Text>
        </div>
      </div>
    </div>
  );
};

export default AirbnbRoomCard;
